# 用户手册

## 目录

1. [快速开始](#快速开始)
2. [Web界面使用](#web界面使用)
3. [命令行工具](#命令行工具)
4. [分析功能详解](#分析功能详解)
5. [可视化功能](#可视化功能)
6. [AI分析功能](#ai分析功能)
7. [常见问题](#常见问题)

## 快速开始

### 安装和配置

1. **环境准备**
   ```bash
   python --version  # 确保Python 3.8+
   ```

2. **安装项目**
   ```bash
   git clone <repository-url>
   cd network-packet-analysis
   pip install -r requirements.txt
   ```

3. **启动Web服务**
   ```bash
   python -m netanalysis.cli.main serve
   ```

4. **访问Web界面**
   打开浏览器访问 `http://localhost:8000`

### 第一次分析

1. 准备PCAP文件（可以使用Wireshark等工具捕获）
2. 在Web界面上传文件
3. 选择分析选项
4. 查看分析结果和可视化图表

## Web界面使用

### 主界面布局

- **侧边栏**: 功能导航菜单
- **仪表板**: 实时统计和图表
- **文件上传**: 拖拽或选择文件上传
- **分析结果**: 详细的分析报告

### 文件上传流程

1. **选择文件**
   - 支持格式: .pcap, .pcapng, .cap
   - 最大文件大小: 100MB
   - 可以拖拽文件到上传区域

2. **配置分析选项**
   - 分析类型: 综合分析/安全分析/性能分析
   - 启用AI分析: 获得智能洞察
   - 生成可视化: 创建交互式图表
   - 安全威胁检测: 识别潜在威胁

3. **查看结果**
   - 实时进度显示
   - 自动跳转到结果页面
   - 可下载分析报告

### 仪表板功能

- **统计卡片**: 总包数、包速率、带宽、异常数
- **流量时间线**: 实时流量变化趋势
- **协议分布**: 各协议使用比例
- **网络拓扑**: 连接关系可视化

## 命令行工具

### 基本命令

```bash
# 查看帮助
python -m netanalysis.cli.main --help

# 查看版本
python -m netanalysis.cli.main version

# 分析文件
python -m netanalysis.cli.main analyze sample.pcap

# 启动Web服务
python -m netanalysis.cli.main serve --port 8080
```

### 高级分析选项

```bash
# 完整分析（包含AI和可视化）
python -m netanalysis.cli.main analyze sample.pcap \
    --visualize \
    --ai \
    --ai-provider openai \
    --analysis-type comprehensive \
    --output report.json \
    --output-dir ./reports

# 安全专项分析
python -m netanalysis.cli.main analyze sample.pcap \
    --analysis-type security \
    --ai \
    --format html \
    --output security_report.html

# 性能分析
python -m netanalysis.cli.main analyze sample.pcap \
    --analysis-type performance \
    --visualize \
    --output-dir ./performance_charts
```

### 实时监控

```bash
# 监控网络接口
python -m netanalysis.cli.main monitor \
    --interface eth0 \
    --duration 300 \
    --real-time

# 保存监控数据
python -m netanalysis.cli.main monitor \
    --interface eth0 \
    --duration 600 \
    --output monitoring_data.pcap
```

## 分析功能详解

### 协议统计分析

- **支持协议**: TCP, UDP, ICMP, HTTP, HTTPS, DNS, FTP等
- **统计指标**: 包数量、字节数、百分比分布
- **详细信息**: 源/目标IP、端口分布、连接状态

### 流量时序分析

- **时间窗口**: 1秒、1分钟、5分钟、1小时
- **关键指标**: 包速率、比特率、连接数
- **趋势分析**: 峰值检测、周期性分析

### 异常检测

- **检测类型**:
  - 流量突发异常
  - 协议分布异常
  - 连接行为异常
  - 包大小异常

- **检测算法**:
  - 统计阈值检测
  - 滑动窗口分析
  - 基线偏差检测

### 安全威胁检测

- **攻击类型**:
  - DDoS攻击
  - 端口扫描
  - 暴力破解
  - 恶意流量

- **检测特征**:
  - 异常连接模式
  - 可疑IP行为
  - 协议滥用
  - 流量特征匹配

## 可视化功能

### 图表类型

1. **时序图表**
   - 流量时间线
   - 协议时间线
   - 带宽使用图

2. **分布图表**
   - 协议分布饼图
   - 端口使用柱状图
   - IP地址分布

3. **网络拓扑图**
   - 连接关系图
   - 流量强度可视化
   - 节点重要性分析

4. **综合仪表板**
   - 多图表集成
   - 实时数据更新
   - 交互式操作

### 交互功能

- **缩放和平移**: 详细查看特定时间段
- **过滤和筛选**: 按协议、IP等条件过滤
- **悬停详情**: 鼠标悬停显示详细信息
- **导出功能**: 保存为PNG、PDF、HTML格式

## AI分析功能

### 支持的AI模型

- **OpenAI GPT**: GPT-3.5-turbo, GPT-4
- **Anthropic Claude**: Claude-3-sonnet, Claude-3-opus
- **Mock模式**: 用于测试和演示

### AI分析类型

1. **综合分析**
   - 全面的网络状况评估
   - 性能和安全综合建议
   - 优化方案推荐

2. **安全分析**
   - 威胁风险评估
   - 安全加固建议
   - 应急响应指导

3. **性能分析**
   - 性能瓶颈识别
   - 优化机会分析
   - 容量规划建议

### AI配置

```python
# OpenAI配置
ai_config = {
    'provider': 'openai',
    'api_key': 'your-openai-api-key',
    'model': 'gpt-3.5-turbo',
    'temperature': 0.7
}

# Claude配置
ai_config = {
    'provider': 'claude',
    'api_key': 'your-anthropic-api-key',
    'model': 'claude-3-sonnet-20240229'
}
```

## 常见问题

### Q: 支持哪些文件格式？
A: 支持标准的PCAP格式文件，包括.pcap、.pcapng、.cap等。

### Q: 文件大小有限制吗？
A: Web界面限制100MB，命令行工具支持更大文件，建议使用流式处理。

### Q: AI分析需要网络连接吗？
A: 是的，OpenAI和Claude需要网络连接。可以使用Mock模式进行离线测试。

### Q: 可视化图表可以导出吗？
A: 可以，支持导出为HTML、PNG、PDF、SVG等格式。

### Q: 如何提高分析性能？
A: 
- 使用SSD存储
- 增加内存
- 启用多线程处理
- 使用数据分批处理

### Q: 支持实时分析吗？
A: 支持，可以通过monitor命令或API接口进行实时分析。

### Q: 如何自定义分析规则？
A: 可以修改配置文件或通过API参数自定义检测阈值和规则。

---

更多问题请查看[GitHub Issues](https://github.com/your-repo/issues)或联系技术支持。
