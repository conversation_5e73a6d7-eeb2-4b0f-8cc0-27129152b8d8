# API 文档

## 概述

网络数据包分析工具提供完整的RESTful API接口，支持文件上传、分析执行、结果查询等功能。

## 基础信息

- **Base URL**: `http://localhost:8000`
- **API版本**: v1.0
- **认证方式**: <PERSON><PERSON>（可选）
- **数据格式**: JSON

## 端点列表

### 1. 健康检查

**GET** `/health`

检查系统健康状态和依赖可用性。

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.0.0",
  "dependencies": {
    "fastapi": true,
    "plotly": true,
    "networkx": true,
    "ai_available": true
  }
}
```

### 2. 文件上传分析

**POST** `/upload`

上传PCAP文件并执行分析。

**请求参数**:
- `file`: 文件对象（multipart/form-data）
- `analysis_type`: 分析类型（comprehensive/security/performance）
- `enable_ai`: 是否启用AI分析（boolean）
- `enable_visualization`: 是否生成可视化（boolean）

**响应示例**:
```json
{
  "success": true,
  "analysis_id": "uuid-string",
  "timestamp": "2024-01-01T12:00:00Z",
  "results": {
    "protocol_stats": {...},
    "traffic_stats": {...},
    "anomalies": [...],
    "security_threats": [...]
  },
  "visualizations": {
    "dashboard": "/static/charts/dashboard.html",
    "timeline": "/static/charts/timeline.html"
  },
  "ai_insights": "AI分析结果文本..."
}
```

### 3. 分析结果查询

**GET** `/analysis/{analysis_id}`

查询指定分析任务的结果。

**路径参数**:
- `analysis_id`: 分析任务ID

**响应**: 与上传分析相同的结果格式

### 4. 实时分析

**POST** `/analyze/realtime`

对实时数据流进行分析。

**请求体**:
```json
{
  "packets": [...],
  "analysis_options": {
    "enable_ai": true,
    "analysis_type": "comprehensive"
  }
}
```

## 错误处理

所有API端点使用标准HTTP状态码：

- `200`: 成功
- `400`: 请求参数错误
- `401`: 认证失败
- `404`: 资源不存在
- `500`: 服务器内部错误

错误响应格式：
```json
{
  "error": {
    "code": "INVALID_FILE_FORMAT",
    "message": "不支持的文件格式",
    "details": "..."
  }
}
```

## 使用示例

### Python客户端示例

```python
import requests

# 上传文件分析
with open('sample.pcap', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/upload',
        files={'file': f},
        data={
            'analysis_type': 'comprehensive',
            'enable_ai': True,
            'enable_visualization': True
        }
    )

result = response.json()
print(f"分析ID: {result['analysis_id']}")
```

### cURL示例

```bash
# 健康检查
curl -X GET http://localhost:8000/health

# 文件上传
curl -X POST http://localhost:8000/upload \
  -F "file=@sample.pcap" \
  -F "analysis_type=comprehensive" \
  -F "enable_ai=true"
```

## 限制说明

- 最大文件大小: 100MB
- 并发分析任务: 10个
- API调用频率: 100次/分钟
- 分析结果保留: 24小时
