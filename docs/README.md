# 网络数据包分析工具

🚀 基于AI的网络数据包分析和可视化平台

## 项目概述

这是一个功能完整的网络数据包分析工具，集成了传统网络分析技术和现代AI技术，提供全面的网络流量分析、异常检测、安全威胁识别和智能化建议功能。

## 核心功能

### 📊 网络分析引擎
- **协议统计分析**：支持TCP、UDP、ICMP等协议的详细统计
- **流量时序分析**：带宽使用、包速率的时间序列分析
- **连接状态分析**：TCP连接生命周期和UDP流分析
- **应用层协议识别**：HTTP、HTTPS、DNS、SSH等协议自动识别

### 🤖 AI智能分析
- **上下文感知分析**：基于网络环境和业务场景的个性化分析
- **智能威胁检测**：使用大语言模型进行高级威胁识别
- **自动化建议生成**：基于分析结果提供优化和安全建议
- **多种分析类型**：综合分析、安全分析、性能分析等

### 🚨 异常检测与安全
- **基于规则的检测**：预定义规则检测已知攻击模式
- **统计异常检测**：基于统计方法识别异常流量
- **DDoS攻击检测**：专门的DDoS攻击识别算法
- **端口扫描检测**：识别端口扫描和网络侦察行为

### 📈 交互式可视化
- **流量时序图**：实时流量趋势和模式可视化
- **协议分布图**：饼图、柱状图展示协议使用情况
- **网络拓扑图**：IP地址连接关系和流量流向
- **交互式仪表板**：综合性的网络监控面板

### 🌐 Web界面与API
- **RESTful API**：完整的Web API接口
- **用户认证**：JWT令牌和API密钥认证
- **文件上传**：支持PCAP文件在线分析
- **实时监控**：Web界面实时显示分析结果

### 💻 命令行工具
- **文件分析**：批量处理PCAP文件
- **可视化生成**：命令行生成图表
- **配置管理**：灵活的配置系统
- **数据导出**：多种格式的结果导出

## 技术架构

### 核心模块
```
src/netanalysis/
├── core/           # 核心分析引擎
│   ├── analyzer.py     # 网络分析器
│   ├── models.py       # 数据模型
│   └── exceptions.py   # 异常定义
├── ai/             # AI集成模块
│   ├── llm_client.py       # LLM客户端
│   ├── openai_client.py    # OpenAI集成
│   ├── prompt_templates.py # 提示词模板
│   └── context_analyzer.py # 上下文分析
├── visualization/ # 可视化模块
│   ├── time_series.py      # 时序图
│   ├── protocol_charts.py  # 协议图表
│   ├── network_topology.py # 网络拓扑
│   └── interactive_charts.py # 交互功能
├── web/           # Web界面
│   ├── api.py          # FastAPI应用
│   ├── middleware.py   # 中间件
│   └── auth.py         # 认证授权
└── cli/           # 命令行工具
    ├── main.py         # CLI主程序
    └── commands.py     # 命令实现
```

### 依赖技术
- **Python 3.8+**：主要开发语言
- **FastAPI**：Web框架和API
- **Plotly**：交互式可视化
- **NetworkX**：网络图分析
- **Click**：命令行界面
- **Rich**：终端美化
- **JWT**：身份认证
- **Pandas**：数据处理

## 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 基础使用
```python
from src.netanalysis.core.analyzer import NetworkAnalyzer
from src.netanalysis.core.models import Packet, ProtocolType

# 创建分析器
analyzer = NetworkAnalyzer()

# 分析数据包
packets = [...]  # 你的数据包列表
protocol_stats = analyzer.analyze_protocols(packets)
traffic_stats = analyzer.analyze_traffic(packets)
anomalies = analyzer.detect_anomalies(packets)
```

### 命令行使用
```bash
# 分析PCAP文件
python -m src.netanalysis.cli.main analyze input.pcap --enable-ai --enable-viz

# 生成可视化
python -m src.netanalysis.cli.main visualize input.pcap --chart-types timeline protocol

# 启动Web服务器
python -m src.netanalysis.cli.main server --host 0.0.0.0 --port 8000
```

### Web API使用
```bash
# 启动服务器
uvicorn src.netanalysis.web.api:app --reload

# 访问API文档
curl http://localhost:8000/docs
```

## 功能演示

### 运行综合演示
```bash
python demo_comprehensive.py
```

这将演示所有核心功能，包括：
- 200个数据包的完整分析
- AI智能分析和建议
- 多种可视化图表生成
- Web API接口测试
- CLI工具功能验证

### 演示结果
- **协议分析**：识别TCP、UDP、ICMP协议分布
- **流量统计**：计算带宽、包速率、连接数
- **异常检测**：基于规则和统计的异常识别
- **AI分析**：智能网络洞察和优化建议
- **可视化**：生成交互式图表和仪表板

## 配置说明

### 基础配置
```json
{
  "ai": {
    "provider": "openai",
    "model": "gpt-3.5-turbo",
    "api_key": "your-api-key"
  },
  "visualization": {
    "theme": "plotly",
    "width": 1200,
    "height": 600
  },
  "analysis": {
    "top_n_limit": 10,
    "enable_geo_analysis": true,
    "anomaly_threshold": 3.0
  }
}
```

### 环境变量
```bash
export OPENAI_API_KEY="your-openai-api-key"
export NETANALYSIS_CONFIG="/path/to/config.json"
export NETANALYSIS_LOG_LEVEL="INFO"
```

## 扩展开发

### 添加新的分析器
```python
class CustomAnalyzer:
    def analyze(self, packets):
        # 实现自定义分析逻辑
        pass

# 注册到主分析器
analyzer.register_plugin(CustomAnalyzer())
```

### 添加新的可视化
```python
class CustomVisualizer:
    def create_chart(self, data):
        # 实现自定义图表
        pass
```

### 添加新的AI提示词
```python
def custom_prompt_template(analysis_type):
    return "自定义分析提示词..."
```

## 性能特点

- **高效处理**：支持大规模数据包的快速分析
- **内存优化**：流式处理避免内存溢出
- **并发支持**：多线程和异步处理
- **缓存机制**：智能缓存提高响应速度
- **可扩展性**：模块化设计支持功能扩展

## 安全特性

- **输入验证**：严格的数据验证和清理
- **访问控制**：基于角色的权限管理
- **审计日志**：完整的操作日志记录
- **数据加密**：敏感数据加密存储
- **安全头**：Web安全响应头设置

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 支持与反馈

- **问题报告**：[GitHub Issues](https://github.com/your-repo/issues)
- **功能请求**：[GitHub Discussions](https://github.com/your-repo/discussions)
- **文档**：[项目文档](docs/)
- **示例**：[示例代码](examples/)

## 更新日志

### v1.0.0 (2025-08-26)
- ✨ 初始版本发布
- 🚀 完整的网络分析功能
- 🤖 AI智能分析集成
- 📈 交互式可视化
- 🌐 Web API接口
- 💻 命令行工具
- 📚 完整文档和示例

---

**网络数据包分析工具** - 让网络分析更智能、更直观、更高效！
