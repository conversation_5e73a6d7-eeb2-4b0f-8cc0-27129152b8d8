# 流量统计分析模块说明文档

## 概述

流量统计分析模块专注于网络流量的量化分析和模式识别。该模块提供全面的流量统计指标，包括带宽使用、包速率、连接分析、时间序列分析等功能，帮助用户深入了解网络流量特征和使用模式。

## 主要功能

### 1. 基础流量统计

- **数据包统计**：总包数、包速率（包/秒）
- **字节统计**：总字节数、字节速率（字节/秒）
- **带宽计算**：比特率（bps）、兆比特率（Mbps）
- **平均包大小**：流量效率指标
- **连接统计**：TCP连接数、UDP流数

### 2. 时间序列分析

- **时间窗口统计**：按秒、分钟、小时统计流量
- **流量趋势分析**：识别流量增长、下降趋势
- **峰值检测**：识别流量高峰时段
- **周期性分析**：识别流量的周期性模式
- **时间分布**：分析流量在时间维度上的分布

### 3. 连接状态分析

- **TCP连接生命周期**：连接建立、维持、关闭过程分析
- **连接持续时间**：分析连接的平均持续时间
- **连接质量评估**：基于重传、超时等指标评估连接质量
- **并发连接数**：分析同时活跃的连接数量
- **连接复用率**：评估连接的复用效率

### 4. 流量方向分析

- **入站流量**：来自外部的流量统计
- **出站流量**：发往外部的流量统计
- **内部流量**：内网之间的流量统计
- **流量不对称性**：分析上行下行流量比例
- **方向性模式**：识别流量的方向性特征

### 5. 地理位置分析

- **IP地址分类**：私有地址、公网地址、环回地址
- **地域分布**：基于IP地址的地理位置分析（可选）
- **网络拓扑**：分析网络连接的拓扑结构
- **跨域流量**：分析跨网络域的流量模式

## 技术实现

### 核心方法

```python
def analyze_traffic(self, packets: List[Packet]) -> TrafficStats:
    """
    流量统计分析主方法
    
    功能：
    - 计算基础流量指标
    - 生成时间序列数据
    - 分析连接状态
    - 统计流量方向
    """
```

### 辅助分析方法

```python
def _analyze_tcp_connections(self, packets: List[Packet]) -> int:
    """TCP连接分析"""
    
def _analyze_udp_flows(self, packets: List[Packet]) -> int:
    """UDP流分析"""
    
def _generate_time_series(self, packets: List[Packet], 
                         start_time: datetime, end_time: datetime) -> Dict[str, int]:
    """时间序列生成"""
    
def _analyze_geo_distribution(self, packets: List[Packet]) -> Dict[str, int]:
    """地理位置分析"""
```

### 数据结构

#### TrafficStats
```python
@dataclass
class TrafficStats:
    # 基础统计
    total_packets: int = 0                 # 总包数
    total_bytes: int = 0                   # 总字节数
    average_packet_size: float = 0.0       # 平均包大小
    
    # 速率统计
    packets_per_second: float = 0.0        # 包速率
    bytes_per_second: float = 0.0          # 字节速率
    bits_per_second: float = 0.0           # 比特率
    
    # 连接统计
    total_connections: int = 0             # 总连接数
    active_connections: int = 0            # 活跃连接数
    failed_connections: int = 0            # 失败连接数
    
    # 时间序列数据
    packet_timeline: List[tuple] = []      # 包时间线
    byte_timeline: List[tuple] = []        # 字节时间线
    connection_timeline: List[tuple] = []  # 连接时间线
    
    # 分布统计
    packet_size_distribution: Dict[str, int] = {}  # 包大小分布
    inter_arrival_times: List[float] = []          # 包间隔时间
    
    # 地理位置统计
    geo_distribution: Dict[str, int] = {}  # 地理分布
```

## 使用示例

### 基础流量分析

```python
from src.netanalysis.core.analyzer import NetworkAnalyzer

# 创建分析器
analyzer = NetworkAnalyzer({
    'enable_geo_analysis': True,
    'top_n_limit': 10
})

# 执行流量分析
traffic_stats = analyzer.analyze_traffic(packets)

# 查看基础统计
print(f"总包数: {traffic_stats.total_packets}")
print(f"总字节数: {traffic_stats.total_bytes}")
print(f"包速率: {traffic_stats.packets_per_second:.2f} 包/秒")
print(f"带宽: {traffic_stats.bits_per_second/1000000:.2f} Mbps")
print(f"平均包大小: {traffic_stats.average_packet_size:.1f} 字节")
```

### 连接分析

```python
# 连接统计
print(f"总连接数: {traffic_stats.total_connections}")
print(f"活跃连接数: {traffic_stats.active_connections}")

# TCP连接详细分析
tcp_connections = analyzer._analyze_tcp_connections(packets)
print(f"TCP连接数: {tcp_connections}")

# UDP流分析
udp_flows = analyzer._analyze_udp_flows(packets)
print(f"UDP流数: {udp_flows}")
```

### 时间序列分析

```python
# 生成时间序列
time_series = analyzer._generate_time_series(packets, start_time, end_time)

# 分析流量趋势
for timestamp, count in time_series.items():
    print(f"{timestamp}: {count} 包")

# 识别流量高峰
peak_time = max(time_series.items(), key=lambda x: x[1])
print(f"流量高峰: {peak_time[0]} - {peak_time[1]} 包")
```

### 地理位置分析

```python
# 地理分布分析
if analyzer.enable_geo_analysis:
    geo_dist = traffic_stats.geo_distribution
    print(f"地理分布: {geo_dist}")
    
    # 分析私有vs公网流量
    private_traffic = geo_dist.get('Private', 0)
    public_traffic = geo_dist.get('Public', 0)
    print(f"私有网络流量: {private_traffic}")
    print(f"公网流量: {public_traffic}")
```

## 分析指标解读

### 流量速率指标

- **包速率 < 100 包/秒**：低流量，可能是测试环境
- **包速率 100-10000 包/秒**：中等流量，正常业务环境
- **包速率 > 10000 包/秒**：高流量，需要关注性能

### 带宽使用指标

- **< 1 Mbps**：低带宽使用，轻度网络活动
- **1-100 Mbps**：中等带宽使用，正常办公环境
- **> 100 Mbps**：高带宽使用，数据中心或高负载环境

### 连接效率指标

- **平均包大小 < 100字节**：大量小包，可能是控制流量
- **平均包大小 100-1000字节**：混合流量，正常应用
- **平均包大小 > 1000字节**：大包传输，文件传输或流媒体

### 连接模式指标

- **短连接为主**：Web浏览、API调用等
- **长连接为主**：数据库连接、持久化服务
- **连接失败率高**：可能存在网络问题或攻击

## 性能优化

### 内存优化

- 使用流式处理避免大量数据包同时加载到内存
- 时间序列数据采用压缩存储
- 定期清理过期的统计数据

### 计算优化

- 并行处理大规模数据包分析
- 缓存重复计算的结果
- 使用高效的数据结构（Counter、defaultdict等）

### 精度控制

- 时间序列精度可配置（秒、分钟、小时）
- 统计窗口大小可调整
- Top N统计数量可配置

## 应用场景

### 网络监控

- 实时流量监控和告警
- 带宽使用率分析
- 网络性能评估

### 容量规划

- 流量增长趋势预测
- 带宽需求评估
- 网络扩容规划

### 故障诊断

- 流量异常检测
- 连接问题分析
- 性能瓶颈识别

### 安全分析

- 异常流量模式识别
- DDoS攻击检测
- 网络行为分析

## 扩展功能

### 高级时间序列分析

- 流量预测模型
- 季节性模式识别
- 异常点检测

### 深度连接分析

- 连接质量评分
- 应用性能分析
- 用户行为分析

### 智能告警

- 自适应阈值设置
- 多维度告警规则
- 告警优先级排序

## 相关模块

- [协议分析模块](protocol_analysis.md)
- [异常检测模块](anomaly_detection.md)
- [可视化模块](visualization.md)
