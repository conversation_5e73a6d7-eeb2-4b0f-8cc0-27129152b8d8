# 协议分析模块说明文档

## 概述

协议分析模块是网络数据包分析工具的核心组件之一，负责对网络数据包进行深度的协议层面分析。该模块能够识别和统计各种网络协议的使用情况，提供详细的协议分布、连接状态和应用层协议识别功能。

## 主要功能

### 1. 基础协议统计

- **协议分布统计**：统计各种网络协议（TCP、UDP、ICMP等）的数据包数量和字节数
- **协议百分比计算**：计算每种协议在总流量中的占比
- **端口使用统计**：分析源端口和目标端口的使用频率
- **IP地址统计**：统计最活跃的源IP和目标IP地址
- **流量统计**：基于五元组识别唯一网络流

### 2. TCP连接状态分析

- **连接数量统计**：基于五元组识别唯一TCP连接
- **连接状态推断**：根据TCP标志位推断连接状态
  - 已建立连接 (established)
  - SYN已发送 (syn_sent)
  - SYN已接收 (syn_received)
  - FIN等待 (fin_wait)
  - 已关闭 (closed)
  - 重置连接 (reset)
  - 未知状态 (unknown)
- **连接质量评估**：分析连接持续时间、数据传输量等指标

### 3. UDP流量分析

- **流数量统计**：基于五元组识别唯一UDP流
- **流模式分析**：
  - 单向流 vs 双向流
  - DNS查询流
  - DHCP事务流
  - 流媒体流
  - 批量传输流
  - 交互式流
- **应用协议识别**：识别基于UDP的应用协议（DNS、DHCP、NTP等）

### 4. 应用层协议识别

- **TCP应用协议**：HTTP、HTTPS、SSH、FTP、SMTP、POP3、IMAP等
- **UDP应用协议**：DNS、DHCP、TFTP、NTP、SNMP等
- **协议特征分析**：
  - HTTP请求/响应分析
  - DNS查询/响应分析
  - 邮件协议流量分析
  - 文件传输协议分析

## 技术实现

### 核心类：NetworkAnalyzer

```python
class NetworkAnalyzer:
    """网络分析器核心类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化分析器，支持自定义配置"""
        
    def analyze_protocols(self, packets: List[Packet]) -> ProtocolStats:
        """协议统计分析"""
        
    def analyze_application_protocols(self, packets: List[Packet]) -> Dict[str, Any]:
        """应用层协议分析"""
        
    def get_protocol_insights(self, packets: List[Packet]) -> Dict[str, Any]:
        """获取协议洞察和建议"""
```

### 配置参数

- `top_n_limit`：统计Top N的数量限制（默认10）
- `enable_geo_analysis`：是否启用地理位置分析（默认False）
- `anomaly_threshold`：异常检测阈值（默认3.0）

### 数据结构

#### ProtocolStats
```python
@dataclass
class ProtocolStats:
    protocol_counts: Dict[str, int]        # 协议包数量统计
    protocol_bytes: Dict[str, int]         # 协议字节数统计
    protocol_percentages: Dict[str, float] # 协议百分比
    top_src_ports: List[tuple]             # Top源端口
    top_dst_ports: List[tuple]             # Top目标端口
    top_src_ips: List[tuple]               # Top源IP
    top_dst_ips: List[tuple]               # Top目标IP
    total_packets: int                     # 总包数
    total_bytes: int                       # 总字节数
    unique_flows: int                      # 唯一流数量
    start_time: Optional[datetime]         # 开始时间
    end_time: Optional[datetime]           # 结束时间
    duration_seconds: float                # 持续时间
```

## 使用示例

### 基础协议分析

```python
from src.netanalysis.core.analyzer import NetworkAnalyzer
from src.netanalysis.core.models import Packet, ProtocolType

# 创建分析器
analyzer = NetworkAnalyzer()

# 创建数据包列表
packets = [
    Packet(
        timestamp=datetime.now(),
        size=64,
        src_ip='***********00',
        dst_ip='***********',
        src_port=12345,
        dst_port=80,
        protocol=ProtocolType.TCP
    )
]

# 执行协议分析
stats = analyzer.analyze_protocols(packets)
print(f"协议分布: {stats.protocol_counts}")
print(f"总流量: {stats.total_bytes} 字节")
```

### 应用层协议分析

```python
# 应用层协议分析
app_analysis = analyzer.analyze_application_protocols(packets)
print(f"识别的协议: {app_analysis['protocol_distribution']}")
print(f"HTTP分析: {app_analysis['http_analysis']}")
print(f"DNS分析: {app_analysis['dns_analysis']}")
```

### 协议洞察分析

```python
# 获取协议洞察
insights = analyzer.get_protocol_insights(packets)
print(f"网络使用模式: {insights['network_usage_pattern']}")
print(f"安全观察: {insights['security_observations']}")
print(f"优化建议: {insights['recommendations']}")
```

## 分析结果解读

### 协议分布分析

- **TCP占比高**：通常表示Web浏览、文件传输等应用较多
- **UDP占比高**：通常表示DNS查询、流媒体等应用较多
- **ICMP占比异常**：可能表示网络故障或攻击行为

### 端口使用分析

- **常见端口**：80(HTTP)、443(HTTPS)、53(DNS)、22(SSH)等
- **异常端口**：大量非标准端口可能表示恶意活动
- **端口扫描**：单一源IP访问大量不同端口

### 连接状态分析

- **大量未知状态**：可能表示数据包捕获不完整
- **大量重置连接**：可能表示网络问题或攻击
- **连接持续时间异常**：可能表示异常的网络行为

## 性能特点

- **高效处理**：支持大规模数据包的快速分析
- **内存优化**：使用流式处理避免内存溢出
- **准确识别**：基于端口号和协议特征的准确协议识别
- **详细统计**：提供多维度的统计分析结果

## 扩展性

模块设计具有良好的扩展性：

1. **新协议支持**：可以轻松添加新的应用协议识别规则
2. **自定义分析**：支持自定义分析逻辑和统计维度
3. **插件化架构**：支持通过插件扩展分析功能
4. **配置灵活**：支持通过配置文件调整分析参数

## 注意事项

1. **数据包完整性**：分析结果的准确性依赖于数据包的完整性
2. **时间戳重要性**：时间序列分析需要准确的时间戳信息
3. **内存使用**：大规模分析时注意内存使用情况
4. **协议识别限制**：基于端口的协议识别可能存在误判

## 相关模块

- [流量统计分析模块](traffic_analysis.md)
- [异常检测模块](anomaly_detection.md)
- [安全威胁检测模块](security_analysis.md)
