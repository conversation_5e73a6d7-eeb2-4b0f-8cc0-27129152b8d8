# 网络数据包分析工具

基于AI的网络数据包分析和可视化平台，提供全面的网络流量分析、安全威胁检测和智能洞察功能。

## 🚀 主要功能

### 核心分析功能
- **协议统计分析**: 支持TCP、UDP、ICMP等多种协议的详细统计
- **流量时序分析**: 实时监控带宽使用、包速率和连接状态
- **异常检测引擎**: 基于规则和统计学的网络异常识别
- **安全威胁检测**: DDoS攻击、端口扫描、恶意流量检测

### AI增强功能
- **智能分析**: 集成OpenAI GPT和Claude等大语言模型
- **上下文感知**: 根据网络环境和业务场景提供定制化分析
- **自动化建议**: AI驱动的网络优化和安全加固建议
- **风险评估**: 智能风险等级评估和优先级排序

### 可视化功能
- **交互式图表**: 基于Plotly的动态可视化图表
- **时序图表**: 流量趋势、协议分布的时间序列分析
- **网络拓扑图**: 自动生成网络连接关系图
- **综合仪表板**: 一站式网络状态监控面板

### 用户界面
- **Web界面**: 现代化的响应式Web用户界面
- **命令行工具**: 功能完整的CLI工具
- **RESTful API**: 标准化的API接口
- **实时监控**: 支持实时网络流量监控

## 📦 安装说明

### 环境要求
- Python 3.8+
- 推荐使用虚拟环境

### 快速安装
```bash
# 克隆项目
git clone <repository-url>
cd network-packet-analysis

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 安装项目
pip install -e .
```

### 可选依赖
```bash
# AI功能依赖
pip install openai anthropic

# 可视化增强
pip install plotly networkx

# Web服务依赖
pip install fastapi uvicorn

# 开发工具
pip install pytest black flake8
```

## 🎯 快速开始

### 1. 命令行使用

#### 分析PCAP文件
```bash
# 基础分析
python -m netanalysis.cli.main analyze sample.pcap

# 生成可视化报告
python -m netanalysis.cli.main analyze sample.pcap --visualize --output-dir ./reports

# 启用AI分析
python -m netanalysis.cli.main analyze sample.pcap --ai --ai-provider openai

# 完整分析（包含所有功能）
python -m netanalysis.cli.main analyze sample.pcap \
    --visualize \
    --ai \
    --ai-provider openai \
    --analysis-type comprehensive \
    --output analysis_report.json
```

#### 启动Web服务
```bash
# 启动Web服务器
python -m netanalysis.cli.main serve --host 0.0.0.0 --port 8080

# 调试模式
python -m netanalysis.cli.main serve --debug --ai-provider mock
```

#### 实时监控
```bash
# 监控网络接口
python -m netanalysis.cli.main monitor --interface eth0 --duration 300

# 实时显示统计
python -m netanalysis.cli.main monitor --interface eth0 --real-time
```

### 2. Python API使用

```python
from netanalysis.core.analyzer import NetworkAnalyzer
from netanalysis.core.packet_parser import PacketParser
from netanalysis.ai.context_analyzer import ContextAwareAnalyzer
from netanalysis.visualization.visualization_engine import VisualizationEngine

# 解析PCAP文件
parser = PacketParser()
packets = parser.parse_file('sample.pcap')

# 执行网络分析
analyzer = NetworkAnalyzer()
protocol_stats = analyzer.analyze_protocols(packets)
traffic_stats = analyzer.analyze_traffic(packets)
anomalies = analyzer.detect_anomalies(packets)
threats = analyzer.detect_security_threats(packets)

# AI分析
ai_analyzer = ContextAwareAnalyzer({'provider': 'openai'})
ai_results = ai_analyzer.analyze_with_context(analysis_data, context)

# 生成可视化
viz_engine = VisualizationEngine()
charts = viz_engine.create_comprehensive_dashboard(packets, protocol_stats, traffic_stats)
```

### 3. Web界面使用

1. 启动Web服务器：`python -m netanalysis.cli.main serve`
2. 打开浏览器访问：`http://localhost:8000`
3. 上传PCAP文件进行分析
4. 查看交互式分析报告和可视化图表

## 📊 功能模块

### 核心分析模块 (`netanalysis.core`)
- `analyzer.py`: 主要分析引擎
- `models.py`: 数据模型定义
- `packet_parser.py`: 数据包解析器
- `anomaly_detector.py`: 异常检测器
- `security_detector.py`: 安全威胁检测器

### AI集成模块 (`netanalysis.ai`)
- `llm_client.py`: LLM客户端抽象接口
- `openai_client.py`: OpenAI API集成
- `claude_client.py`: Claude API集成
- `context_analyzer.py`: 上下文感知分析器
- `prompt_templates.py`: 提示词模板

### 可视化模块 (`netanalysis.visualization`)
- `visualization_engine.py`: 可视化引擎
- `time_series.py`: 时序图表
- `protocol_charts.py`: 协议分布图表
- `network_topology.py`: 网络拓扑图
- `interactive_charts.py`: 交互式图表

### Web模块 (`netanalysis.web`)
- `api.py`: FastAPI应用
- `auth.py`: 认证模块
- `middleware.py`: 中间件
- `static/`: 静态文件

### CLI模块 (`netanalysis.cli`)
- `main.py`: 命令行主程序
- `commands.py`: 命令实现

## 🔧 配置说明

### AI配置
```python
ai_config = {
    'provider': 'openai',  # 或 'claude', 'mock'
    'api_key': 'your-api-key',
    'model': 'gpt-3.5-turbo',  # 或其他模型
    'temperature': 0.7,
    'max_tokens': 1000
}
```

### 可视化配置
```python
viz_config = {
    'theme': 'plotly_white',
    'width': 1200,
    'height': 600,
    'output_dir': './visualizations',
    'auto_save': True
}
```

### Web服务配置
```python
web_config = {
    'host': '0.0.0.0',
    'port': 8000,
    'debug': False,
    'cors_origins': ['*']
}
```

## 📈 性能特性

- **高性能处理**: 支持每秒处理10万+数据包
- **内存优化**: 流式处理大型PCAP文件
- **并发分析**: 多线程并行处理
- **缓存机制**: 智能结果缓存
- **增量分析**: 支持增量数据分析

## 🛡️ 安全特性

- **威胁检测**: 多种攻击模式识别
- **异常监控**: 实时异常行为检测
- **隐私保护**: 敏感数据脱敏处理
- **访问控制**: 基于角色的权限管理

## 🧪 测试

```bash
# 运行单元测试
pytest tests/unit/

# 运行集成测试
pytest tests/integration/

# 运行性能测试
python performance_test.py

# 代码覆盖率
pytest --cov=netanalysis tests/
```

## 📚 文档

- [API文档](docs/api.md)
- [用户手册](docs/user_guide.md)
- [开发者指南](docs/developer_guide.md)
- [部署指南](docs/deployment.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Scapy](https://scapy.net/) - 网络数据包处理
- [Plotly](https://plotly.com/) - 交互式可视化
- [FastAPI](https://fastapi.tiangolo.com/) - 现代Web框架
- [OpenAI](https://openai.com/) - AI分析能力
- [Anthropic](https://www.anthropic.com/) - Claude AI模型

## 📞 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: [<EMAIL>]

---

**网络数据包分析工具** - 让网络分析更智能、更直观、更高效！
