#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络数据包分析工具
Network Packet Analysis Tool

一个综合性的网络数据包分析平台，支持多种抓包格式的解析、分析和可视化，
并集成AI技术提供智能化的分析建议。

主要功能：
- 支持多种数据包格式解析（PCAP、PCAPNG、tcpdump等）
- 协议层级分析和流量统计
- 异常检测和安全威胁识别
- AI增强的智能分析
- 丰富的可视化展示
- Web界面和命令行工具

作者：开发团队
版本：1.0.0
许可证：MIT
"""

__version__ = "1.0.0"
__author__ = "开发团队"
__email__ = "<EMAIL>"
__license__ = "MIT"
__description__ = "综合性网络数据包分析工具"

# 导出主要的公共接口
from .core.analyzer import NetworkAnalyzer
from .core.parser import PacketParser
from .core.exceptions import (
    NetAnalysisError,
    ParseError,
    AnalysisError,
    ConfigError,
)

# 版本信息
VERSION_INFO = {
    "major": 1,
    "minor": 0,
    "patch": 0,
    "release": "stable",
    "build": None,
}

# 支持的文件格式
SUPPORTED_FORMATS = [
    "pcap",
    "pcapng", 
    "cap",
    "dmp",
    "txt",  # tcpdump文本输出
    "json", # tshark JSON输出
]

# 支持的协议
SUPPORTED_PROTOCOLS = [
    # 数据链路层
    "ethernet",
    "arp",
    "vlan",
    
    # 网络层
    "ipv4",
    "ipv6", 
    "icmp",
    "icmpv6",
    
    # 传输层
    "tcp",
    "udp",
    "sctp",
    
    # 应用层
    "http",
    "https",
    "dns",
    "dhcp",
    "smtp",
    "pop3",
    "imap",
    "ftp",
    "ssh",
    "telnet",
    "snmp",
]

# 默认配置
DEFAULT_CONFIG = {
    "max_file_size": 1024 * 1024 * 1024,  # 1GB
    "chunk_size": 1024 * 1024,  # 1MB
    "timeout": 3600,  # 1小时
    "max_memory": 2 * 1024 * 1024 * 1024,  # 2GB
    "log_level": "INFO",
}

def get_version() -> str:
    """获取版本字符串"""
    return __version__

def get_version_info() -> dict:
    """获取详细版本信息"""
    return VERSION_INFO.copy()

def get_supported_formats() -> list:
    """获取支持的文件格式列表"""
    return SUPPORTED_FORMATS.copy()

def get_supported_protocols() -> list:
    """获取支持的协议列表"""
    return SUPPORTED_PROTOCOLS.copy()

# 模块级别的日志配置
import logging

# 创建模块日志记录器
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 如果没有处理器，添加一个默认的控制台处理器
if not logger.handlers:
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    console_handler.setFormatter(formatter)
    
    logger.addHandler(console_handler)

# 模块初始化日志
logger.info(f"网络数据包分析工具 v{__version__} 已加载")
logger.info(f"支持的文件格式: {', '.join(SUPPORTED_FORMATS)}")
logger.info(f"支持的协议数量: {len(SUPPORTED_PROTOCOLS)}")

# 导出所有公共接口
__all__ = [
    # 版本信息
    "__version__",
    "__author__", 
    "__email__",
    "__license__",
    "__description__",
    "VERSION_INFO",
    
    # 核心类
    "NetworkAnalyzer",
    "PacketParser",
    
    # 异常类
    "NetAnalysisError",
    "ParseError", 
    "AnalysisError",
    "ConfigError",
    
    # 常量
    "SUPPORTED_FORMATS",
    "SUPPORTED_PROTOCOLS",
    "DEFAULT_CONFIG",
    
    # 工具函数
    "get_version",
    "get_version_info",
    "get_supported_formats",
    "get_supported_protocols",
]
