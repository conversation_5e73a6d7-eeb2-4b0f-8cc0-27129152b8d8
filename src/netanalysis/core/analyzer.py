#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络分析器核心模块
Network Analyzer Core Module

提供网络数据包的分析功能，包括：
- 协议统计分析：协议分布、端口统计、IP统计
- 流量模式分析：带宽使用、连接分析、时间序列
- 异常检测：基于规则和统计的异常识别
- 安全威胁识别：DDoS、端口扫描等攻击检测

实现了完整的网络分析功能，支持多种协议和分析维度。
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import ipaddress
from dataclasses import asdict

from .models import (
    Packet, ProtocolStats, TrafficStats, Anomaly, SecurityThreat,
    ProtocolType, PacketDirection, AnomalyType, ThreatLevel
)
from .exceptions import AnalysisError

logger = logging.getLogger(__name__)


class NetworkAnalyzer:
    """
    网络分析器

    提供完整的网络数据包分析功能，包括协议统计、流量分析、
    异常检测和安全威胁识别。
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化网络分析器

        Args:
            config: 分析器配置参数，包括：
                - top_n_limit: 统计Top N的数量限制（默认10）
                - enable_geo_analysis: 是否启用地理位置分析（默认False）
                - anomaly_threshold: 异常检测阈值（默认3.0）
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.NetworkAnalyzer")

        # 配置参数
        self.top_n_limit = self.config.get('top_n_limit', 10)
        self.enable_geo_analysis = self.config.get('enable_geo_analysis', False)
        self.anomaly_threshold = self.config.get('anomaly_threshold', 3.0)

        self.logger.info(f"网络分析器初始化完成，配置: {self.config}")

    def analyze_protocols(self, packets: List[Packet]) -> ProtocolStats:
        """
        分析协议统计信息

        对数据包进行协议层面的统计分析，包括：
        - 协议分布统计（包数量和字节数）
        - 端口使用统计（源端口和目标端口）
        - IP地址统计（源IP和目标IP）
        - 时间范围和总体统计

        Args:
            packets: 数据包列表

        Returns:
            ProtocolStats: 协议统计结果

        Raises:
            AnalysisError: 分析过程中出现错误
        """
        if not packets:
            self.logger.warning("数据包列表为空，返回空的协议统计结果")
            return ProtocolStats()

        try:
            self.logger.info(f"开始分析 {len(packets)} 个数据包的协议统计信息")

            # 初始化统计计数器
            protocol_counts = Counter()
            protocol_bytes = Counter()
            src_ports = Counter()
            dst_ports = Counter()
            src_ips = Counter()
            dst_ips = Counter()

            total_packets = len(packets)
            total_bytes = 0
            unique_flows = set()

            # 时间范围统计
            timestamps = [p.timestamp for p in packets if p.timestamp]
            start_time = min(timestamps) if timestamps else None
            end_time = max(timestamps) if timestamps else None
            duration = (end_time - start_time).total_seconds() if start_time and end_time else 0.0

            # 遍历数据包进行统计
            for packet in packets:
                # 协议统计
                protocol = packet.protocol.value if packet.protocol else 'unknown'
                protocol_counts[protocol] += 1
                protocol_bytes[protocol] += packet.size
                total_bytes += packet.size

                # 端口统计（仅对TCP/UDP）
                if packet.src_port:
                    src_ports[packet.src_port] += 1
                if packet.dst_port:
                    dst_ports[packet.dst_port] += 1

                # IP统计
                if packet.src_ip:
                    src_ips[packet.src_ip] += 1
                if packet.dst_ip:
                    dst_ips[packet.dst_ip] += 1

                # 流统计（基于五元组，考虑双向性）
                if packet.src_ip and packet.dst_ip:
                    # 为了正确识别双向流，我们需要标准化流标识符
                    # 将较小的IP:端口组合放在前面
                    src_addr = (packet.src_ip, packet.src_port or 0)
                    dst_addr = (packet.dst_ip, packet.dst_port or 0)

                    if src_addr < dst_addr:
                        flow_key = (src_addr[0], src_addr[1], dst_addr[0], dst_addr[1], protocol)
                    else:
                        flow_key = (dst_addr[0], dst_addr[1], src_addr[0], src_addr[1], protocol)

                    unique_flows.add(flow_key)

            # 计算协议百分比
            protocol_percentages = {
                proto: (count / total_packets * 100)
                for proto, count in protocol_counts.items()
            }

            # 构建统计结果
            stats = ProtocolStats(
                protocol_counts=dict(protocol_counts),
                protocol_bytes=dict(protocol_bytes),
                protocol_percentages=protocol_percentages,
                top_src_ports=src_ports.most_common(self.top_n_limit),
                top_dst_ports=dst_ports.most_common(self.top_n_limit),
                top_src_ips=src_ips.most_common(self.top_n_limit),
                top_dst_ips=dst_ips.most_common(self.top_n_limit),
                total_packets=total_packets,
                total_bytes=total_bytes,
                unique_flows=len(unique_flows),
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration
            )

            self.logger.info(f"协议统计分析完成: {total_packets}包, {total_bytes}字节, "
                           f"{len(unique_flows)}流, {len(protocol_counts)}种协议")

            return stats

        except Exception as e:
            error_msg = f"协议统计分析失败: {str(e)}"
            self.logger.error(error_msg)
            raise AnalysisError(error_msg) from e
    
    def analyze_traffic(self, packets: List[Packet]) -> TrafficStats:
        """
        分析流量统计信息

        对数据包进行流量层面的统计分析，包括：
        - 基础流量指标（带宽、包速率、连接数）
        - 时间序列分析（流量随时间变化）
        - 连接状态分析（TCP连接生命周期）
        - 地理位置分析（如果启用）

        Args:
            packets: 数据包列表

        Returns:
            TrafficStats: 流量统计结果

        Raises:
            AnalysisError: 分析过程中出现错误
        """
        if not packets:
            self.logger.warning("数据包列表为空，返回空的流量统计结果")
            return TrafficStats()

        try:
            self.logger.info(f"开始分析 {len(packets)} 个数据包的流量统计信息")

            # 基础统计
            total_packets = len(packets)
            total_bytes = sum(p.size for p in packets)

            # 时间范围分析
            timestamps = [p.timestamp for p in packets if p.timestamp]
            if not timestamps:
                self.logger.warning("没有有效的时间戳信息")
                return TrafficStats()

            start_time = min(timestamps)
            end_time = max(timestamps)
            duration = (end_time - start_time).total_seconds()

            # 计算速率
            packets_per_second = total_packets / duration if duration > 0 else 0
            bytes_per_second = total_bytes / duration if duration > 0 else 0
            bits_per_second = bytes_per_second * 8

            # 连接统计
            tcp_connections = self._analyze_tcp_connections(packets)
            udp_flows = self._analyze_udp_flows(packets)

            # 时间序列分析（按秒分组）
            time_series = self._generate_time_series(packets, start_time, end_time)

            # 方向统计
            inbound_packets = sum(1 for p in packets if p.direction == PacketDirection.INBOUND)
            outbound_packets = sum(1 for p in packets if p.direction == PacketDirection.OUTBOUND)
            internal_packets = sum(1 for p in packets if p.direction == PacketDirection.INTERNAL)

            inbound_bytes = sum(p.size for p in packets if p.direction == PacketDirection.INBOUND)
            outbound_bytes = sum(p.size for p in packets if p.direction == PacketDirection.OUTBOUND)
            internal_bytes = sum(p.size for p in packets if p.direction == PacketDirection.INTERNAL)

            # 地理位置分析（如果启用）
            geo_distribution = {}
            if self.enable_geo_analysis:
                geo_distribution = self._analyze_geo_distribution(packets)

            # 构建流量统计结果
            stats = TrafficStats(
                total_packets=total_packets,
                total_bytes=total_bytes,
                average_packet_size=total_bytes / total_packets if total_packets > 0 else 0,
                packets_per_second=packets_per_second,
                bytes_per_second=bytes_per_second,
                bits_per_second=bits_per_second,
                total_connections=tcp_connections + udp_flows,
                active_connections=tcp_connections,
                geo_distribution=geo_distribution
            )

            self.logger.info(f"流量统计分析完成: {total_packets}包, {total_bytes}字节, "
                           f"{packets_per_second:.2f}包/秒, {bits_per_second:.2f}bps")

            return stats

        except Exception as e:
            error_msg = f"流量统计分析失败: {str(e)}"
            self.logger.error(error_msg)
            raise AnalysisError(error_msg) from e
    
    def _analyze_tcp_connections(self, packets: List[Packet]) -> int:
        """
        分析TCP连接数量和状态

        对TCP连接进行详细分析，包括：
        - 连接数量统计
        - 连接状态分析（建立、活跃、关闭）
        - 连接持续时间分析
        - 连接质量评估

        Args:
            packets: 数据包列表

        Returns:
            int: TCP连接数量
        """
        tcp_packets = [p for p in packets if p.protocol == ProtocolType.TCP]
        if not tcp_packets:
            return 0

        # 基于五元组识别唯一连接
        connections = {}
        connection_stats = {}

        for packet in tcp_packets:
            if packet.src_ip and packet.dst_ip and packet.src_port and packet.dst_port:
                # 创建连接标识符（标准化方向）
                conn_key = self._normalize_connection_key(
                    packet.src_ip, packet.src_port,
                    packet.dst_ip, packet.dst_port
                )

                # 初始化连接统计
                if conn_key not in connections:
                    connections[conn_key] = {
                        'first_seen': packet.timestamp,
                        'last_seen': packet.timestamp,
                        'packet_count': 0,
                        'total_bytes': 0,
                        'syn_count': 0,
                        'fin_count': 0,
                        'rst_count': 0,
                        'ack_count': 0
                    }

                # 更新连接统计
                conn_stats = connections[conn_key]
                conn_stats['last_seen'] = packet.timestamp
                conn_stats['packet_count'] += 1
                conn_stats['total_bytes'] += packet.size

                # 分析TCP标志位（如果可用）
                if hasattr(packet, 'tcp_flags'):
                    flags = packet.tcp_flags
                    if flags & 0x02:  # SYN
                        conn_stats['syn_count'] += 1
                    if flags & 0x01:  # FIN
                        conn_stats['fin_count'] += 1
                    if flags & 0x04:  # RST
                        conn_stats['rst_count'] += 1
                    if flags & 0x10:  # ACK
                        conn_stats['ack_count'] += 1

        # 分析连接状态和质量
        self._analyze_connection_states(connections)

        return len(connections)

    def _normalize_connection_key(self, src_ip: str, src_port: int,
                                dst_ip: str, dst_port: int) -> tuple:
        """
        标准化连接键值，确保同一连接的双向流量使用相同的键

        Args:
            src_ip: 源IP地址
            src_port: 源端口
            dst_ip: 目标IP地址
            dst_port: 目标端口

        Returns:
            tuple: 标准化的连接键值
        """
        # 使用字典序较小的端点作为连接的"起始"端点
        endpoint1 = (src_ip, src_port)
        endpoint2 = (dst_ip, dst_port)

        if endpoint1 < endpoint2:
            return (src_ip, src_port, dst_ip, dst_port)
        else:
            return (dst_ip, dst_port, src_ip, src_port)

    def _analyze_connection_states(self, connections: Dict) -> Dict[str, int]:
        """
        分析TCP连接状态分布

        Args:
            connections: 连接统计字典

        Returns:
            Dict[str, int]: 连接状态分布统计
        """
        state_counts = {
            'established': 0,    # 已建立连接
            'syn_sent': 0,       # SYN已发送
            'syn_received': 0,   # SYN已接收
            'fin_wait': 0,       # FIN等待
            'closed': 0,         # 已关闭
            'reset': 0,          # 重置连接
            'unknown': 0         # 未知状态
        }

        for conn_key, stats in connections.items():
            # 基于TCP标志位推断连接状态
            if stats['rst_count'] > 0:
                state_counts['reset'] += 1
            elif stats['fin_count'] >= 2:  # 双向FIN
                state_counts['closed'] += 1
            elif stats['fin_count'] > 0:
                state_counts['fin_wait'] += 1
            elif stats['syn_count'] > 0 and stats['ack_count'] > 0:
                state_counts['established'] += 1
            elif stats['syn_count'] > 0:
                state_counts['syn_sent'] += 1
            else:
                state_counts['unknown'] += 1

        # 记录连接状态分析结果
        self.logger.info(f"TCP连接状态分析: {state_counts}")

        return state_counts

    def _analyze_udp_flows(self, packets: List[Packet]) -> int:
        """
        分析UDP流数量和特征

        对UDP流进行详细分析，包括：
        - 流数量统计
        - 流量模式分析（单向/双向）
        - 应用协议识别（DNS、DHCP等）
        - 流持续时间和活跃度分析

        Args:
            packets: 数据包列表

        Returns:
            int: UDP流数量
        """
        udp_packets = [p for p in packets if p.protocol == ProtocolType.UDP]
        if not udp_packets:
            return 0

        # 基于五元组识别唯一流
        flows = {}
        flow_patterns = {}

        for packet in udp_packets:
            if packet.src_ip and packet.dst_ip and packet.src_port and packet.dst_port:
                # 创建流标识符
                flow_key = self._normalize_connection_key(
                    packet.src_ip, packet.src_port,
                    packet.dst_ip, packet.dst_port
                )

                # 初始化流统计
                if flow_key not in flows:
                    flows[flow_key] = {
                        'first_seen': packet.timestamp,
                        'last_seen': packet.timestamp,
                        'packet_count': 0,
                        'total_bytes': 0,
                        'src_to_dst_packets': 0,
                        'dst_to_src_packets': 0,
                        'src_to_dst_bytes': 0,
                        'dst_to_src_bytes': 0,
                        'application_protocol': 'unknown'
                    }

                # 更新流统计
                flow_stats = flows[flow_key]
                flow_stats['last_seen'] = packet.timestamp
                flow_stats['packet_count'] += 1
                flow_stats['total_bytes'] += packet.size

                # 分析流方向
                original_src = flow_key[0]
                original_dst = flow_key[2]

                if packet.src_ip == original_src:
                    flow_stats['src_to_dst_packets'] += 1
                    flow_stats['src_to_dst_bytes'] += packet.size
                else:
                    flow_stats['dst_to_src_packets'] += 1
                    flow_stats['dst_to_src_bytes'] += packet.size

                # 识别应用协议
                app_protocol = self._identify_udp_application_protocol(packet)
                if app_protocol != 'unknown':
                    flow_stats['application_protocol'] = app_protocol

        # 分析UDP流模式
        self._analyze_udp_flow_patterns(flows)

        return len(flows)

    def _identify_udp_application_protocol(self, packet: Packet) -> str:
        """
        识别UDP应用层协议

        Args:
            packet: UDP数据包

        Returns:
            str: 应用协议名称
        """
        # 基于端口号识别常见的UDP协议
        well_known_ports = {
            53: 'dns',
            67: 'dhcp_server',
            68: 'dhcp_client',
            69: 'tftp',
            123: 'ntp',
            161: 'snmp',
            162: 'snmp_trap',
            514: 'syslog',
            520: 'rip',
            1900: 'upnp',
            5353: 'mdns'
        }

        # 检查目标端口
        if packet.dst_port in well_known_ports:
            return well_known_ports[packet.dst_port]

        # 检查源端口
        if packet.src_port in well_known_ports:
            return well_known_ports[packet.src_port]

        # 检查高端口范围的常见协议
        if packet.dst_port and packet.dst_port >= 32768:
            return 'ephemeral'
        elif packet.src_port and packet.src_port >= 32768:
            return 'ephemeral'

        return 'unknown'

    def _analyze_udp_flow_patterns(self, flows: Dict) -> Dict[str, int]:
        """
        分析UDP流模式

        Args:
            flows: UDP流统计字典

        Returns:
            Dict[str, int]: 流模式分布统计
        """
        pattern_counts = {
            'unidirectional': 0,    # 单向流
            'bidirectional': 0,     # 双向流
            'dns_query': 0,         # DNS查询
            'dhcp_transaction': 0,  # DHCP事务
            'streaming': 0,         # 流媒体
            'bulk_transfer': 0,     # 批量传输
            'interactive': 0        # 交互式
        }

        for flow_key, stats in flows.items():
            # 判断流方向性
            if stats['dst_to_src_packets'] == 0:
                pattern_counts['unidirectional'] += 1
            else:
                pattern_counts['bidirectional'] += 1

            # 基于应用协议分类
            app_protocol = stats['application_protocol']
            if app_protocol == 'dns':
                pattern_counts['dns_query'] += 1
            elif app_protocol in ['dhcp_server', 'dhcp_client']:
                pattern_counts['dhcp_transaction'] += 1

            # 基于流量特征分类
            duration = (stats['last_seen'] - stats['first_seen']).total_seconds()
            avg_packet_size = stats['total_bytes'] / stats['packet_count']

            if stats['total_bytes'] > 1000000:  # 超过1MB
                pattern_counts['bulk_transfer'] += 1
            elif duration > 60 and stats['packet_count'] > 100:  # 长时间多包
                pattern_counts['streaming'] += 1
            elif avg_packet_size < 100:  # 小包
                pattern_counts['interactive'] += 1

        # 记录UDP流模式分析结果
        self.logger.info(f"UDP流模式分析: {pattern_counts}")

        return pattern_counts

    def analyze_application_protocols(self, packets: List[Packet]) -> Dict[str, Any]:
        """
        分析应用层协议分布和特征

        对数据包进行应用层协议识别和分析，包括：
        - HTTP/HTTPS流量分析
        - DNS查询分析
        - 邮件协议分析（SMTP、POP3、IMAP）
        - 文件传输协议分析（FTP、SFTP）
        - 其他常见应用协议

        Args:
            packets: 数据包列表

        Returns:
            Dict[str, Any]: 应用层协议分析结果
        """
        if not packets:
            return {}

        try:
            self.logger.info(f"开始分析 {len(packets)} 个数据包的应用层协议")

            # 协议计数器
            app_protocol_counts = Counter()
            app_protocol_bytes = Counter()

            # 详细分析结果
            http_analysis = {
                'requests': 0,
                'responses': 0,
                'request_bytes': 0,
                'response_bytes': 0,
                'request_types': defaultdict(int),
                'response_types': defaultdict(int),
                'servers': defaultdict(int)
            }

            dns_analysis = {
                'queries': 0,
                'responses': 0,
                'query_bytes': 0,
                'response_bytes': 0,
                'query_types': defaultdict(int),
                'response_types': defaultdict(int),
                'servers': defaultdict(int),
                'public_dns_usage': 0
            }

            email_analysis = {
                'smtp_outbound': 0,
                'smtp_inbound': 0,
                'smtp_outbound_bytes': 0,
                'smtp_inbound_bytes': 0,
                'smtp_secure': 0,
                'pop3_outbound': 0,
                'pop3_inbound': 0,
                'pop3_outbound_bytes': 0,
                'pop3_inbound_bytes': 0,
                'pop3_secure': 0,
                'imap_outbound': 0,
                'imap_inbound': 0,
                'imap_outbound_bytes': 0,
                'imap_inbound_bytes': 0,
                'imap_secure': 0,
                'servers': defaultdict(int)
            }

            ftp_analysis = {
                'commands': 0,
                'data_transfers': 0,
                'command_bytes': 0,
                'data_bytes': 0,
                'servers': defaultdict(int)
            }

            for packet in packets:
                # 基于端口和协议识别应用层协议
                app_protocol = self._identify_application_protocol(packet)
                app_protocol_counts[app_protocol] += 1
                app_protocol_bytes[app_protocol] += packet.size

                # 详细分析特定协议
                if app_protocol == 'http' or app_protocol == 'https':
                    self._analyze_http_packet(packet, http_analysis)
                elif app_protocol == 'dns':
                    self._analyze_dns_packet(packet, dns_analysis)
                elif app_protocol in ['smtp', 'pop3', 'imap']:
                    self._analyze_email_packet(packet, email_analysis, app_protocol)
                elif app_protocol == 'ftp':
                    self._analyze_ftp_packet(packet, ftp_analysis)

            # 构建分析结果
            result = {
                'protocol_distribution': dict(app_protocol_counts),
                'protocol_bytes': dict(app_protocol_bytes),
                'total_protocols': len(app_protocol_counts),
                'http_analysis': http_analysis,
                'dns_analysis': dns_analysis,
                'email_analysis': email_analysis,
                'ftp_analysis': ftp_analysis,
                'top_protocols': app_protocol_counts.most_common(10)
            }

            self.logger.info(f"应用层协议分析完成: 识别出 {len(app_protocol_counts)} 种协议")

            return result

        except Exception as e:
            error_msg = f"应用层协议分析失败: {str(e)}"
            self.logger.error(error_msg)
            raise AnalysisError(error_msg) from e

    def _identify_application_protocol(self, packet: Packet) -> str:
        """
        识别应用层协议

        Args:
            packet: 数据包

        Returns:
            str: 应用协议名称
        """
        # TCP协议的应用层识别
        if packet.protocol == ProtocolType.TCP:
            return self._identify_tcp_application_protocol(packet)

        # UDP协议的应用层识别
        elif packet.protocol == ProtocolType.UDP:
            return self._identify_udp_application_protocol(packet)

        # 其他协议
        else:
            return packet.protocol.value if packet.protocol else 'unknown'

    def _identify_tcp_application_protocol(self, packet: Packet) -> str:
        """
        识别TCP应用层协议

        Args:
            packet: TCP数据包

        Returns:
            str: 应用协议名称
        """
        # 基于端口号识别常见的TCP协议
        well_known_ports = {
            # 文件传输协议
            20: 'ftp_data',
            21: 'ftp',
            22: 'ssh',
            23: 'telnet',

            # 邮件协议
            25: 'smtp',
            110: 'pop3',
            143: 'imap',
            465: 'smtp',      # SMTPS
            587: 'smtp',      # SMTP提交端口
            993: 'imap',      # IMAPS
            995: 'pop3',      # POP3S

            # Web协议
            80: 'http',
            443: 'https',
            8000: 'http',
            8001: 'http',
            8080: 'http',
            8443: 'https',
            8888: 'http',
            9000: 'http',

            # DNS
            53: 'dns',

            # 数据库协议
            1433: 'mssql',
            1521: 'oracle',
            3306: 'mysql',
            5432: 'postgresql',
            6379: 'redis',
            27017: 'mongodb',

            # 远程访问协议
            3389: 'rdp',
            5900: 'vnc',

            # 其他常见协议
            135: 'rpc',
            139: 'netbios',
            445: 'smb',
            636: 'ldaps',
            989: 'ftps_data',
            990: 'ftps',
            1194: 'openvpn',
            1723: 'pptp',
            5060: 'sip',
            5061: 'sips'
        }

        # 检查目标端口（优先级更高，通常是服务端口）
        if packet.dst_port in well_known_ports:
            return well_known_ports[packet.dst_port]

        # 检查源端口（可能是服务响应）
        if packet.src_port in well_known_ports:
            return well_known_ports[packet.src_port]

        # 检查端口范围和模式
        if packet.dst_port:
            # HTTP替代端口范围
            if 8000 <= packet.dst_port <= 8999:
                return 'http'
            # HTTPS替代端口范围
            elif packet.dst_port in [8443, 9443]:
                return 'https'
            # 高端口范围（临时端口）
            elif packet.dst_port >= 32768:
                return 'ephemeral'
            # 其他常见端口范围
            elif 1024 <= packet.dst_port <= 5000:
                return 'tcp_custom'

        # 检查源端口的模式
        if packet.src_port:
            if packet.src_port >= 32768:
                return 'ephemeral'

        return 'tcp_unknown'

    def _analyze_http_packet(self, packet: Packet, http_analysis: Dict) -> None:
        """
        分析HTTP数据包

        对HTTP流量进行详细分析，包括请求/响应识别、方法统计、状态码分析等

        Args:
            packet: HTTP数据包
            http_analysis: HTTP分析结果字典
        """
        # 扩展的HTTP端口列表
        http_ports = [80, 8080, 8000, 8001, 8002, 8003, 8008, 8888, 9000, 9080]

        # 基于端口和包大小判断是请求还是响应
        if packet.dst_port in http_ports:
            http_analysis['requests'] += 1
            http_analysis['request_bytes'] += packet.size

            # 基于包大小推断请求类型
            if packet.size < 200:
                http_analysis['request_types']['GET'] += 1
            elif packet.size < 1000:
                http_analysis['request_types']['POST'] += 1
            else:
                http_analysis['request_types']['large_request'] += 1

            # 记录请求的目标服务器
            if packet.dst_ip:
                http_analysis['servers'][packet.dst_ip] += 1

        elif packet.src_port in http_ports:
            http_analysis['responses'] += 1
            http_analysis['response_bytes'] += packet.size

            # 基于包大小推断响应类型
            if packet.size < 500:
                http_analysis['response_types']['small'] += 1  # 可能是错误响应或重定向
            elif packet.size < 5000:
                http_analysis['response_types']['medium'] += 1  # 普通页面
            else:
                http_analysis['response_types']['large'] += 1   # 大文件或富媒体内容

            # 记录响应的源服务器
            if packet.src_ip:
                http_analysis['servers'][packet.src_ip] += 1

    def _analyze_dns_packet(self, packet: Packet, dns_analysis: Dict) -> None:
        """
        分析DNS数据包

        对DNS流量进行详细分析，包括查询/响应识别、查询类型推断、服务器统计等

        Args:
            packet: DNS数据包
            dns_analysis: DNS分析结果字典
        """
        # 基于端口和包大小判断是查询还是响应
        if packet.dst_port == 53:
            dns_analysis['queries'] += 1
            dns_analysis['query_bytes'] += packet.size

            # 基于包大小推断查询类型
            if packet.size <= 50:
                dns_analysis['query_types']['A'] += 1  # A记录查询（最常见）
            elif packet.size <= 80:
                dns_analysis['query_types']['AAAA'] += 1  # IPv6地址查询
            elif packet.size <= 120:
                dns_analysis['query_types']['MX'] += 1  # 邮件交换记录
            else:
                dns_analysis['query_types']['complex'] += 1  # 复杂查询（TXT、SRV等）

            # 记录DNS服务器
            if packet.dst_ip:
                dns_analysis['servers'][packet.dst_ip] += 1

            # 检查是否为递归查询（通常发送到公共DNS）
            if packet.dst_ip in ['*******', '*******', '*******', '*******']:
                dns_analysis['public_dns_usage'] += 1

        elif packet.src_port == 53:
            dns_analysis['responses'] += 1
            dns_analysis['response_bytes'] += packet.size

            # 基于包大小推断响应类型
            if packet.size <= 100:
                dns_analysis['response_types']['simple'] += 1  # 简单响应
            elif packet.size <= 300:
                dns_analysis['response_types']['normal'] += 1  # 正常响应
            else:
                dns_analysis['response_types']['complex'] += 1  # 复杂响应（多记录）

            # 记录DNS服务器
            if packet.src_ip:
                dns_analysis['servers'][packet.src_ip] += 1

    def _analyze_email_packet(self, packet: Packet, email_analysis: Dict, protocol: str) -> None:
        """
        分析邮件协议数据包（SMTP、POP3、IMAP）

        对邮件协议流量进行详细分析，包括连接类型、数据传输模式等

        Args:
            packet: 邮件协议数据包
            email_analysis: 邮件分析结果字典
            protocol: 协议类型（smtp、pop3、imap）
        """
        protocol_ports = {
            'smtp': [25, 465, 587],  # SMTP, SMTPS, 提交端口
            'pop3': [110, 995],      # POP3, POP3S
            'imap': [143, 993]       # IMAP, IMAPS
        }

        ports = protocol_ports.get(protocol, [])

        if packet.dst_port in ports:
            email_analysis[f'{protocol}_outbound'] += 1
            email_analysis[f'{protocol}_outbound_bytes'] += packet.size

            # 记录邮件服务器
            if packet.dst_ip:
                email_analysis['servers'][packet.dst_ip] += 1

            # 检查是否为加密连接
            if protocol == 'smtp' and packet.dst_port in [465, 587]:
                email_analysis['smtp_secure'] += 1
            elif protocol == 'pop3' and packet.dst_port == 995:
                email_analysis['pop3_secure'] += 1
            elif protocol == 'imap' and packet.dst_port == 993:
                email_analysis['imap_secure'] += 1

        elif packet.src_port in ports:
            email_analysis[f'{protocol}_inbound'] += 1
            email_analysis[f'{protocol}_inbound_bytes'] += packet.size

            # 记录邮件服务器
            if packet.src_ip:
                email_analysis['servers'][packet.src_ip] += 1

    def _analyze_ftp_packet(self, packet: Packet, ftp_analysis: Dict) -> None:
        """
        分析FTP数据包

        对FTP流量进行详细分析，包括控制连接和数据传输

        Args:
            packet: FTP数据包
            ftp_analysis: FTP分析结果字典
        """
        if packet.dst_port == 21 or packet.src_port == 21:
            ftp_analysis['commands'] += 1
            ftp_analysis['command_bytes'] += packet.size

            # 记录FTP服务器
            if packet.dst_port == 21 and packet.dst_ip:
                ftp_analysis['servers'][packet.dst_ip] += 1
            elif packet.src_port == 21 and packet.src_ip:
                ftp_analysis['servers'][packet.src_ip] += 1

        elif packet.dst_port == 20 or packet.src_port == 20:
            ftp_analysis['data_transfers'] += 1
            ftp_analysis['data_bytes'] += packet.size

    def get_protocol_insights(self, packets: List[Packet]) -> Dict[str, Any]:
        """
        获取协议洞察和建议

        基于协议分析结果提供网络使用洞察和优化建议

        Args:
            packets: 数据包列表

        Returns:
            Dict[str, Any]: 协议洞察和建议
        """
        if not packets:
            return {}

        try:
            # 获取基础统计
            protocol_stats = self.analyze_protocols(packets)
            app_protocol_analysis = self.analyze_application_protocols(packets)

            insights = {
                'network_usage_pattern': self._analyze_network_usage_pattern(protocol_stats),
                'security_observations': self._analyze_security_observations(app_protocol_analysis),
                'performance_insights': self._analyze_performance_insights(protocol_stats),
                'recommendations': self._generate_recommendations(protocol_stats, app_protocol_analysis),
                'protocol_quality': self._assess_protocol_quality(app_protocol_analysis),
                'network_health_score': self._calculate_network_health_score(protocol_stats, app_protocol_analysis)
            }

            return insights

        except Exception as e:
            error_msg = f"协议洞察分析失败: {str(e)}"
            self.logger.error(error_msg)
            return {'error': error_msg}

    def _analyze_network_usage_pattern(self, protocol_stats: ProtocolStats) -> Dict[str, str]:
        """
        分析网络使用模式

        Args:
            protocol_stats: 协议统计信息

        Returns:
            Dict[str, str]: 网络使用模式分析
        """
        patterns = {}

        # 分析协议分布
        tcp_percentage = protocol_stats.protocol_percentages.get('tcp', 0)
        udp_percentage = protocol_stats.protocol_percentages.get('udp', 0)

        if tcp_percentage > 80:
            patterns['traffic_type'] = '以TCP为主的网络流量，主要是Web浏览、文件传输等应用'
        elif udp_percentage > 50:
            patterns['traffic_type'] = '以UDP为主的网络流量，主要是DNS查询、流媒体等应用'
        else:
            patterns['traffic_type'] = 'TCP和UDP混合流量，网络使用较为均衡'

        # 分析流量规模
        if protocol_stats.total_packets > 10000:
            patterns['traffic_volume'] = '高流量网络，建议监控带宽使用'
        elif protocol_stats.total_packets > 1000:
            patterns['traffic_volume'] = '中等流量网络，使用正常'
        else:
            patterns['traffic_volume'] = '低流量网络，可能是测试环境或轻度使用'

        return patterns

    def _analyze_security_observations(self, app_analysis: Dict[str, Any]) -> List[str]:
        """
        分析安全观察

        Args:
            app_analysis: 应用层协议分析结果

        Returns:
            List[str]: 安全观察列表
        """
        observations = []

        protocol_dist = app_analysis.get('protocol_distribution', {})

        # 检查加密协议使用
        https_count = protocol_dist.get('https', 0)
        http_count = protocol_dist.get('http', 0)

        if http_count > 0 and https_count == 0:
            observations.append('检测到HTTP流量但无HTTPS，建议使用加密连接')
        elif https_count > http_count:
            observations.append('良好的安全实践：主要使用HTTPS加密连接')

        # 检查敏感协议
        if protocol_dist.get('telnet', 0) > 0:
            observations.append('检测到Telnet流量，建议使用SSH替代')

        if protocol_dist.get('ftp', 0) > 0:
            observations.append('检测到FTP流量，建议使用SFTP或FTPS')

        return observations

    def _analyze_performance_insights(self, protocol_stats: ProtocolStats) -> List[str]:
        """
        分析性能洞察

        Args:
            protocol_stats: 协议统计信息

        Returns:
            List[str]: 性能洞察列表
        """
        insights = []

        # 分析包大小分布
        avg_packet_size = protocol_stats.total_bytes / protocol_stats.total_packets if protocol_stats.total_packets > 0 else 0

        if avg_packet_size < 100:
            insights.append('平均包大小较小，可能存在大量控制流量或小文件传输')
        elif avg_packet_size > 1000:
            insights.append('平均包大小较大，网络传输效率较好')

        # 分析连接效率
        if protocol_stats.unique_flows > 0:
            packets_per_flow = protocol_stats.total_packets / protocol_stats.unique_flows
            if packets_per_flow < 10:
                insights.append('每个流的包数较少，可能存在大量短连接')
            elif packets_per_flow > 100:
                insights.append('每个流的包数较多，连接复用效率较好')

        return insights

    def _generate_recommendations(self, protocol_stats: ProtocolStats,
                                app_analysis: Dict[str, Any]) -> List[str]:
        """
        生成优化建议

        Args:
            protocol_stats: 协议统计信息
            app_analysis: 应用层协议分析结果

        Returns:
            List[str]: 优化建议列表
        """
        recommendations = []

        # 基于协议分布的建议
        protocol_dist = app_analysis.get('protocol_distribution', {})

        if protocol_dist.get('http', 0) > protocol_dist.get('https', 0):
            recommendations.append('建议将HTTP流量迁移到HTTPS以提高安全性')

        if protocol_stats.total_packets > 50000:
            recommendations.append('考虑实施流量监控和带宽管理策略')

        if protocol_stats.unique_flows > 1000:
            recommendations.append('考虑优化连接池配置以减少连接开销')

        # 基于DNS使用的建议
        dns_count = protocol_dist.get('dns', 0)
        if dns_count > protocol_stats.total_packets * 0.1:
            recommendations.append('DNS查询较多，考虑配置DNS缓存优化')

        return recommendations

    def _generate_time_series(self, packets: List[Packet], start_time: datetime,
                            end_time: datetime) -> Dict[str, int]:
        """
        生成时间序列数据（按秒统计包数量）

        Args:
            packets: 数据包列表
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            Dict[str, int]: 时间序列数据，键为时间戳字符串，值为包数量
        """
        time_series = defaultdict(int)

        for packet in packets:
            if packet.timestamp:
                # 将时间戳向下取整到秒
                timestamp_key = packet.timestamp.strftime('%Y-%m-%d %H:%M:%S')
                time_series[timestamp_key] += 1

        return dict(time_series)

    def _analyze_geo_distribution(self, packets: List[Packet]) -> Dict[str, int]:
        """
        分析地理位置分布（占位符实现）

        Args:
            packets: 数据包列表

        Returns:
            Dict[str, int]: 地理位置分布统计
        """
        # TODO: 实现真实的地理位置分析
        # 这里只是一个占位符实现
        geo_dist = defaultdict(int)

        for packet in packets:
            if packet.src_ip:
                try:
                    ip = ipaddress.ip_address(packet.src_ip)
                    if ip.is_private:
                        geo_dist['Private'] += 1
                    elif ip.is_loopback:
                        geo_dist['Loopback'] += 1
                    else:
                        geo_dist['Public'] += 1
                except ValueError:
                    geo_dist['Invalid'] += 1

        return dict(geo_dist)

    def detect_anomalies(self, packets: List[Packet]) -> List[Anomaly]:
        """
        检测网络异常

        使用统计方法和规则检测网络异常，包括：
        - 流量异常（异常高的包速率、字节速率）
        - 连接异常（异常多的连接数）
        - 协议异常（异常的协议分布）

        Args:
            packets: 数据包列表

        Returns:
            List[Anomaly]: 检测到的异常列表
        """
        if not packets:
            return []

        try:
            self.logger.info(f"开始检测 {len(packets)} 个数据包中的异常")
            anomalies = []

            # 获取流量统计
            traffic_stats = self.analyze_traffic(packets)

            # 检测流量异常
            anomalies.extend(self._detect_traffic_anomalies(traffic_stats))

            # 检测协议异常
            protocol_stats = self.analyze_protocols(packets)
            anomalies.extend(self._detect_protocol_anomalies(protocol_stats))

            self.logger.info(f"异常检测完成，发现 {len(anomalies)} 个异常")
            return anomalies

        except Exception as e:
            error_msg = f"异常检测失败: {str(e)}"
            self.logger.error(error_msg)
            raise AnalysisError(error_msg) from e
    
    def _detect_traffic_anomalies(self, traffic_stats: TrafficStats) -> List[Anomaly]:
        """
        检测流量异常

        Args:
            traffic_stats: 流量统计信息

        Returns:
            List[Anomaly]: 检测到的流量异常列表
        """
        anomalies = []

        # 检测异常高的包速率（简单阈值检测）
        if traffic_stats.packets_per_second > 10000:  # 每秒超过1万包
            anomalies.append(Anomaly(
                anomaly_id=f"traffic_high_pps_{int(traffic_stats.packets_per_second)}",
                anomaly_type=AnomalyType.STATISTICAL,
                severity=ThreatLevel.HIGH,
                confidence=0.8,
                title="异常高包速率",
                description=f"检测到异常高的包速率: {traffic_stats.packets_per_second:.2f} 包/秒",
                detected_at=datetime.now(),
                details={
                    "packets_per_second": traffic_stats.packets_per_second,
                    "threshold": 10000
                }
            ))

        # 检测异常高的带宽使用
        if traffic_stats.bits_per_second > 1000000000:  # 超过1Gbps
            anomalies.append(Anomaly(
                anomaly_id=f"traffic_high_bps_{int(traffic_stats.bits_per_second)}",
                anomaly_type=AnomalyType.STATISTICAL,
                severity=ThreatLevel.HIGH,
                confidence=0.8,
                title="异常高带宽使用",
                description=f"检测到异常高的带宽使用: {traffic_stats.bits_per_second/1000000:.2f} Mbps",
                detected_at=datetime.now(),
                details={
                    "bits_per_second": traffic_stats.bits_per_second,
                    "threshold": 1000000000
                }
            ))

        return anomalies

    def _detect_protocol_anomalies(self, protocol_stats: ProtocolStats) -> List[Anomaly]:
        """
        检测协议异常

        Args:
            protocol_stats: 协议统计信息

        Returns:
            List[Anomaly]: 检测到的协议异常列表
        """
        anomalies = []

        # 检测异常的协议分布
        total_packets = protocol_stats.total_packets
        if total_packets > 0:
            # 检测ICMP流量异常（超过总流量的50%）
            icmp_count = protocol_stats.protocol_counts.get('icmp', 0)
            if icmp_count / total_packets > 0.5:
                anomalies.append(Anomaly(
                    anomaly_id=f"protocol_high_icmp_{icmp_count}",
                    anomaly_type=AnomalyType.RULE_BASED,
                    severity=ThreatLevel.MEDIUM,
                    confidence=0.9,
                    title="异常高ICMP流量",
                    description=f"检测到异常高的ICMP流量: {icmp_count}/{total_packets} ({icmp_count/total_packets*100:.1f}%)",
                    detected_at=datetime.now(),
                    details={
                        "icmp_packets": icmp_count,
                        "total_packets": total_packets,
                        "percentage": icmp_count/total_packets*100
                    }
                ))

        return anomalies

    def detect_security_threats(self, packets: List[Packet]) -> List[SecurityThreat]:
        """
        检测安全威胁

        检测各种网络安全威胁，包括：
        - DDoS攻击检测
        - 端口扫描检测
        - 恶意软件通信检测
        - 异常连接模式检测

        Args:
            packets: 数据包列表

        Returns:
            List[SecurityThreat]: 检测到的威胁列表
        """
        if not packets:
            return []

        try:
            self.logger.info(f"开始检测 {len(packets)} 个数据包中的安全威胁")
            threats = []

            # DDoS攻击检测
            threats.extend(self._detect_ddos_attacks(packets))

            # 端口扫描检测
            threats.extend(self._detect_port_scans(packets))

            # 异常连接检测
            threats.extend(self._detect_suspicious_connections(packets))

            self.logger.info(f"安全威胁检测完成，发现 {len(threats)} 个威胁")
            return threats

        except Exception as e:
            error_msg = f"安全威胁检测失败: {str(e)}"
            self.logger.error(error_msg)
            raise AnalysisError(error_msg) from e


    def _detect_ddos_attacks(self, packets: List[Packet]) -> List[SecurityThreat]:
        """
        检测DDoS攻击

        Args:
            packets: 数据包列表

        Returns:
            List[SecurityThreat]: 检测到的DDoS威胁列表
        """
        threats = []

        # 统计目标IP的连接数
        target_ip_counts = Counter()
        for packet in packets:
            if packet.dst_ip:
                target_ip_counts[packet.dst_ip] += 1

        # 检测是否有IP收到异常多的连接
        total_packets = len(packets)
        for ip, count in target_ip_counts.items():
            if count > total_packets * 0.3:  # 超过30%的流量指向同一IP
                threats.append(SecurityThreat(
                    threat_id=f"ddos_target_{ip}_{count}",
                    threat_type="ddos",
                    severity=ThreatLevel.HIGH,
                    confidence=0.8,
                    title="可能的DDoS攻击",
                    description=f"检测到可能的DDoS攻击，目标IP: {ip}，收到 {count} 个数据包 ({count/total_packets*100:.1f}%)",
                    attack_vector="网络层DDoS",
                    detected_at=datetime.now(),
                    target_ips=[ip],
                    attack_packets=count,
                    indicators={
                        "target_ip": ip,
                        "packet_count": count,
                        "percentage": count/total_packets*100
                    }
                ))

        return threats

    def _detect_port_scans(self, packets: List[Packet]) -> List[SecurityThreat]:
        """
        检测端口扫描

        Args:
            packets: 数据包列表

        Returns:
            List[SecurityThreat]: 检测到的端口扫描威胁列表
        """
        threats = []

        # 统计每个源IP访问的不同目标端口数量
        src_ip_ports = defaultdict(set)
        for packet in packets:
            if packet.src_ip and packet.dst_port:
                src_ip_ports[packet.src_ip].add(packet.dst_port)

        # 检测访问大量不同端口的IP（可能是端口扫描）
        for src_ip, ports in src_ip_ports.items():
            if len(ports) > 50:  # 访问超过50个不同端口
                threats.append(SecurityThreat(
                    threat_id=f"port_scan_{src_ip}_{len(ports)}",
                    threat_type="port_scan",
                    severity=ThreatLevel.MEDIUM,
                    confidence=0.7,
                    title="可能的端口扫描",
                    description=f"检测到可能的端口扫描，源IP: {src_ip}，扫描了 {len(ports)} 个端口",
                    attack_vector="端口扫描",
                    detected_at=datetime.now(),
                    source_ips=[src_ip],
                    target_ports=sorted(list(ports))[:20],  # 只记录前20个端口
                    indicators={
                        "source_ip": src_ip,
                        "scanned_ports": len(ports),
                        "port_list": sorted(list(ports))[:20]
                    }
                ))

        return threats

    def _detect_suspicious_connections(self, packets: List[Packet]) -> List[SecurityThreat]:
        """
        检测可疑连接

        Args:
            packets: 数据包列表

        Returns:
            List[SecurityThreat]: 检测到的可疑连接威胁列表
        """
        threats = []

        # 检测连接到异常端口的流量
        suspicious_ports = {22, 23, 135, 139, 445, 1433, 3389, 5432}  # 常见的攻击目标端口
        suspicious_connections = defaultdict(int)

        for packet in packets:
            if packet.dst_port in suspicious_ports:
                key = (packet.src_ip, packet.dst_ip, packet.dst_port)
                suspicious_connections[key] += 1

        # 报告异常连接
        for (src_ip, dst_ip, port), count in suspicious_connections.items():
            if count > 10:  # 超过10个包到敏感端口
                threats.append(SecurityThreat(
                    threat_id=f"suspicious_conn_{src_ip}_{dst_ip}_{port}",
                    threat_type="suspicious_connection",
                    severity=ThreatLevel.MEDIUM,
                    confidence=0.6,
                    title="可疑连接",
                    description=f"检测到可疑连接: {src_ip} -> {dst_ip}:{port}，{count} 个数据包",
                    attack_vector="可疑端口连接",
                    detected_at=datetime.now(),
                    source_ips=[src_ip] if src_ip else [],
                    target_ips=[dst_ip] if dst_ip else [],
                    target_ports=[port],
                    attack_packets=count,
                    indicators={
                        "source_ip": src_ip,
                        "target_ip": dst_ip,
                        "target_port": port,
                        "packet_count": count
                    }
                ))

        return threats

    def _assess_protocol_quality(self, app_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估协议使用质量

        Args:
            app_analysis: 应用层协议分析结果

        Returns:
            Dict[str, Any]: 协议质量评估结果
        """
        quality_assessment = {
            'encryption_score': 0,
            'security_score': 0,
            'efficiency_score': 0,
            'recommendations': []
        }

        protocol_dist = app_analysis.get('protocol_distribution', {})
        total_traffic = sum(protocol_dist.values())

        if total_traffic == 0:
            return quality_assessment

        # 加密协议使用评分
        encrypted_protocols = ['https', 'ssh', 'imaps', 'pop3s', 'ftps']
        encrypted_count = sum(protocol_dist.get(p, 0) for p in encrypted_protocols)
        encryption_ratio = encrypted_count / total_traffic
        quality_assessment['encryption_score'] = min(100, encryption_ratio * 100)

        # 安全协议评分
        insecure_protocols = ['http', 'telnet', 'ftp']
        insecure_count = sum(protocol_dist.get(p, 0) for p in insecure_protocols)
        security_ratio = 1 - (insecure_count / total_traffic)
        quality_assessment['security_score'] = max(0, security_ratio * 100)

        # 效率评分（基于协议选择的合理性）
        efficiency_score = 80  # 基础分数
        if protocol_dist.get('dns', 0) > total_traffic * 0.2:
            efficiency_score -= 10  # DNS查询过多
        if protocol_dist.get('tcp_unknown', 0) > total_traffic * 0.1:
            efficiency_score -= 15  # 未知协议过多

        quality_assessment['efficiency_score'] = max(0, efficiency_score)

        return quality_assessment

    def _calculate_network_health_score(self, protocol_stats: ProtocolStats,
                                      app_analysis: Dict[str, Any]) -> int:
        """
        计算网络健康评分

        Args:
            protocol_stats: 协议统计信息
            app_analysis: 应用层协议分析结果

        Returns:
            int: 网络健康评分（0-100）
        """
        score = 100  # 起始满分

        # 协议分布健康度
        protocol_dist = app_analysis.get('protocol_distribution', {})
        total_protocols = len(protocol_dist)

        if total_protocols > 20:
            score -= 10  # 协议过于复杂
        elif total_protocols < 3:
            score -= 5   # 协议过于单一

        # 流量分布健康度
        if protocol_stats.total_packets > 0:
            # 检查是否有异常大的流量集中
            max_protocol_ratio = max(protocol_dist.values()) / protocol_stats.total_packets
            if max_protocol_ratio > 0.9:
                score -= 15  # 流量过于集中

        # 安全性评分
        insecure_protocols = ['http', 'telnet', 'ftp']
        insecure_count = sum(protocol_dist.get(p, 0) for p in insecure_protocols)
        if insecure_count > 0:
            score -= min(20, insecure_count * 2)

        return max(0, min(100, score))


class AnalysisResult:
    """
    分析结果类

    封装完整的网络分析结果，包括协议统计、流量分析、
    异常检测和安全威胁识别的所有结果。
    """

    def __init__(self, analysis_id: str = ""):
        """
        初始化分析结果

        Args:
            analysis_id: 分析任务ID
        """
        self.analysis_id = analysis_id or f"analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.timestamp = datetime.now()
        self.protocol_stats: Optional[ProtocolStats] = None
        self.traffic_stats: Optional[TrafficStats] = None
        self.anomalies: List[Anomaly] = []
        self.security_threats: List[SecurityThreat] = []
        self.metadata: Dict[str, Any] = {}

    def to_dict(self) -> Dict[str, Any]:
        """
        将分析结果转换为字典格式

        Returns:
            Dict[str, Any]: 分析结果字典
        """
        return {
            "analysis_id": self.analysis_id,
            "timestamp": self.timestamp.isoformat(),
            "protocol_stats": asdict(self.protocol_stats) if self.protocol_stats else None,
            "traffic_stats": asdict(self.traffic_stats) if self.traffic_stats else None,
            "anomalies": [asdict(anomaly) for anomaly in self.anomalies],
            "security_threats": [asdict(threat) for threat in self.security_threats],
            "metadata": self.metadata
        }

    def get_summary(self) -> Dict[str, Any]:
        """
        获取分析结果摘要

        Returns:
            Dict[str, Any]: 分析结果摘要
        """
        return {
            "analysis_id": self.analysis_id,
            "timestamp": self.timestamp.isoformat(),
            "total_packets": self.protocol_stats.total_packets if self.protocol_stats else 0,
            "total_bytes": self.protocol_stats.total_bytes if self.protocol_stats else 0,
            "duration_seconds": self.protocol_stats.duration_seconds if self.protocol_stats else 0,
            "unique_protocols": len(self.protocol_stats.protocol_counts) if self.protocol_stats else 0,
            "anomaly_count": len(self.anomalies),
            "threat_count": len(self.security_threats),
            "high_severity_issues": len([
                item for item in (self.anomalies + self.security_threats)
                if hasattr(item, 'severity') and item.severity == ThreatLevel.HIGH
            ])
        }
