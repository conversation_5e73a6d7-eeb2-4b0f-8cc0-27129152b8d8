#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络分析工具函数
Network Analysis Utility Functions

提供网络数据包分析中常用的工具函数，包括：
- 文件验证和格式检测
- 数据格式化和转换
- 哈希计算和校验
- 网络地址处理
- 时间处理工具
"""

import os
import hashlib
import mimetypes
import ipaddress
import struct
from pathlib import Path
from typing import Optional, Union, List, Dict, Any, Tuple
from datetime import datetime, timedelta
import logging

from .exceptions import ValidationError, FileFormatError

logger = logging.getLogger(__name__)


def validate_file(file_path: str, max_size: Optional[int] = None) -> bool:
    """
    验证文件是否存在且可读
    
    Args:
        file_path: 文件路径
        max_size: 最大文件大小（字节），None表示不限制
        
    Returns:
        bool: 文件是否有效
        
    Raises:
        ValidationError: 文件验证失败
    """
    try:
        path = Path(file_path)
        
        # 检查文件是否存在
        if not path.exists():
            raise ValidationError(f"文件不存在: {file_path}")
        
        # 检查是否为文件
        if not path.is_file():
            raise ValidationError(f"路径不是文件: {file_path}")
        
        # 检查文件是否可读
        if not os.access(file_path, os.R_OK):
            raise ValidationError(f"文件不可读: {file_path}")
        
        # 检查文件大小
        file_size = path.stat().st_size
        if file_size == 0:
            raise ValidationError(f"文件为空: {file_path}")
        
        if max_size and file_size > max_size:
            raise ValidationError(
                f"文件过大: {file_size} > {max_size} 字节",
                details={"file_size": file_size, "max_size": max_size}
            )
        
        return True
    
    except ValidationError:
        raise
    except Exception as e:
        raise ValidationError(f"文件验证失败: {e}", cause=e)


def detect_file_format(file_path: str) -> str:
    """
    检测文件格式
    
    Args:
        file_path: 文件路径
        
    Returns:
        str: 文件格式（扩展名）
        
    Raises:
        FileFormatError: 无法检测文件格式
    """
    try:
        path = Path(file_path)
        
        # 首先尝试从扩展名判断
        extension = path.suffix.lower().lstrip('.')
        if extension:
            # 已知的网络抓包格式
            known_formats = {
                'pcap', 'pcapng', 'cap', 'dmp', 'snoop', 'nettl',
                'txt', 'json', 'csv', 'xml'
            }
            if extension in known_formats:
                return extension
        
        # 尝试通过文件头检测格式
        with open(file_path, 'rb') as f:
            header = f.read(16)
        
        # PCAP文件魔数检测
        if len(header) >= 4:
            magic = struct.unpack('<I', header[:4])[0]
            if magic == 0xa1b2c3d4:  # PCAP little-endian
                return 'pcap'
            elif magic == 0xd4c3b2a1:  # PCAP big-endian
                return 'pcap'
        
        # PCAPNG文件魔数检测
        if len(header) >= 8:
            if header[:4] == b'\x0a\x0d\x0d\x0a':  # PCAPNG
                return 'pcapng'
        
        # 尝试使用mimetypes模块
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type:
            # 根据MIME类型推断格式
            mime_to_format = {
                'application/vnd.tcpdump.pcap': 'pcap',
                'application/json': 'json',
                'text/plain': 'txt',
                'text/csv': 'csv',
                'application/xml': 'xml',
            }
            if mime_type in mime_to_format:
                return mime_to_format[mime_type]
        
        # 如果都无法检测，返回扩展名或unknown
        return extension if extension else 'unknown'
    
    except Exception as e:
        raise FileFormatError(f"无法检测文件格式: {e}", file_path=file_path, cause=e)


def calculate_file_hash(file_path: str, algorithm: str = 'sha256') -> str:
    """
    计算文件哈希值
    
    Args:
        file_path: 文件路径
        algorithm: 哈希算法（md5, sha1, sha256等）
        
    Returns:
        str: 文件哈希值（十六进制字符串）
        
    Raises:
        ValidationError: 计算哈希失败
    """
    try:
        hash_obj = hashlib.new(algorithm)
        
        with open(file_path, 'rb') as f:
            # 分块读取文件以节省内存
            for chunk in iter(lambda: f.read(8192), b''):
                hash_obj.update(chunk)
        
        return hash_obj.hexdigest()
    
    except Exception as e:
        raise ValidationError(f"计算文件哈希失败: {e}", file_path=file_path, cause=e)


def format_bytes(bytes_count: int, decimal_places: int = 2) -> str:
    """
    格式化字节数为人类可读的字符串
    
    Args:
        bytes_count: 字节数
        decimal_places: 小数位数
        
    Returns:
        str: 格式化后的字符串（如 "1.23 MB"）
    """
    if bytes_count == 0:
        return "0 B"
    
    units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
    unit_index = 0
    size = float(bytes_count)
    
    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1
    
    return f"{size:.{decimal_places}f} {units[unit_index]}"


def format_duration(seconds: float) -> str:
    """
    格式化时间长度为人类可读的字符串
    
    Args:
        seconds: 秒数
        
    Returns:
        str: 格式化后的字符串（如 "1h 23m 45s"）
    """
    if seconds < 0:
        return "0s"
    
    # 转换为整数秒
    total_seconds = int(seconds)
    
    # 计算各个时间单位
    days = total_seconds // 86400
    hours = (total_seconds % 86400) // 3600
    minutes = (total_seconds % 3600) // 60
    secs = total_seconds % 60
    
    # 构建结果字符串
    parts = []
    if days > 0:
        parts.append(f"{days}d")
    if hours > 0:
        parts.append(f"{hours}h")
    if minutes > 0:
        parts.append(f"{minutes}m")
    if secs > 0 or not parts:  # 如果没有其他单位，至少显示秒
        parts.append(f"{secs}s")
    
    return " ".join(parts)


def format_rate(rate: float, unit: str = "bps") -> str:
    """
    格式化速率为人类可读的字符串
    
    Args:
        rate: 速率值
        unit: 单位（bps, pps等）
        
    Returns:
        str: 格式化后的字符串（如 "1.23 Mbps"）
    """
    if rate == 0:
        return f"0 {unit}"
    
    prefixes = ['', 'K', 'M', 'G', 'T']
    prefix_index = 0
    value = float(rate)
    
    while value >= 1000 and prefix_index < len(prefixes) - 1:
        value /= 1000
        prefix_index += 1
    
    return f"{value:.2f} {prefixes[prefix_index]}{unit}"


def validate_ip_address(ip_str: str) -> bool:
    """
    验证IP地址格式
    
    Args:
        ip_str: IP地址字符串
        
    Returns:
        bool: 是否为有效的IP地址
    """
    try:
        ipaddress.ip_address(ip_str)
        return True
    except ValueError:
        return False


def is_private_ip(ip_str: str) -> bool:
    """
    检查是否为私有IP地址
    
    Args:
        ip_str: IP地址字符串
        
    Returns:
        bool: 是否为私有IP地址
    """
    try:
        ip = ipaddress.ip_address(ip_str)
        return ip.is_private
    except ValueError:
        return False


def get_ip_version(ip_str: str) -> Optional[int]:
    """
    获取IP地址版本
    
    Args:
        ip_str: IP地址字符串
        
    Returns:
        Optional[int]: IP版本（4或6），无效地址返回None
    """
    try:
        ip = ipaddress.ip_address(ip_str)
        return ip.version
    except ValueError:
        return None


def normalize_mac_address(mac_str: str) -> str:
    """
    标准化MAC地址格式
    
    Args:
        mac_str: MAC地址字符串
        
    Returns:
        str: 标准化的MAC地址（xx:xx:xx:xx:xx:xx格式）
        
    Raises:
        ValidationError: MAC地址格式无效
    """
    try:
        # 移除所有分隔符
        mac_clean = ''.join(c for c in mac_str if c.isalnum())
        
        # 检查长度
        if len(mac_clean) != 12:
            raise ValidationError(f"MAC地址长度无效: {mac_str}")
        
        # 检查是否为十六进制
        int(mac_clean, 16)
        
        # 格式化为标准格式
        return ':'.join(mac_clean[i:i+2] for i in range(0, 12, 2)).lower()
    
    except ValueError:
        raise ValidationError(f"MAC地址格式无效: {mac_str}")


def parse_timestamp(timestamp: Union[int, float, str, datetime]) -> datetime:
    """
    解析时间戳为datetime对象
    
    Args:
        timestamp: 时间戳（Unix时间戳、字符串或datetime对象）
        
    Returns:
        datetime: 解析后的datetime对象
        
    Raises:
        ValidationError: 时间戳格式无效
    """
    try:
        if isinstance(timestamp, datetime):
            return timestamp
        
        if isinstance(timestamp, (int, float)):
            # Unix时间戳
            return datetime.fromtimestamp(timestamp)
        
        if isinstance(timestamp, str):
            # 尝试解析字符串格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d %H:%M:%S.%f',
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%dT%H:%M:%S.%f',
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%dT%H:%M:%S.%fZ',
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(timestamp, fmt)
                except ValueError:
                    continue
            
            # 尝试解析为Unix时间戳
            try:
                return datetime.fromtimestamp(float(timestamp))
            except ValueError:
                pass
        
        raise ValidationError(f"无法解析时间戳: {timestamp}")
    
    except Exception as e:
        if isinstance(e, ValidationError):
            raise
        raise ValidationError(f"时间戳解析失败: {e}", cause=e)


def create_flow_id(src_ip: str, dst_ip: str, src_port: int, dst_port: int, protocol: str) -> str:
    """
    创建网络流ID
    
    Args:
        src_ip: 源IP地址
        dst_ip: 目标IP地址
        src_port: 源端口
        dst_port: 目标端口
        protocol: 协议类型
        
    Returns:
        str: 流ID字符串
    """
    # 标准化流的表示（较小的IP和端口在前）
    if src_ip < dst_ip or (src_ip == dst_ip and src_port < dst_port):
        flow_tuple = (src_ip, dst_ip, src_port, dst_port, protocol.lower())
    else:
        flow_tuple = (dst_ip, src_ip, dst_port, src_port, protocol.lower())
    
    # 生成哈希ID
    flow_str = '|'.join(str(x) for x in flow_tuple)
    return hashlib.md5(flow_str.encode()).hexdigest()[:16]


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    安全除法，避免除零错误
    
    Args:
        numerator: 分子
        denominator: 分母
        default: 除零时的默认值
        
    Returns:
        float: 除法结果
    """
    try:
        if denominator == 0:
            return default
        return numerator / denominator
    except (TypeError, ValueError):
        return default


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    将列表分块
    
    Args:
        lst: 要分块的列表
        chunk_size: 每块的大小
        
    Returns:
        List[List[Any]]: 分块后的列表
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """
    深度合并两个字典
    
    Args:
        dict1: 第一个字典
        dict2: 第二个字典
        
    Returns:
        Dict[str, Any]: 合并后的字典
    """
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = merge_dicts(result[key], value)
        else:
            result[key] = value
    
    return result
