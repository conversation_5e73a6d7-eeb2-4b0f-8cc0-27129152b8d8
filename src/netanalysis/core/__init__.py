#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络分析核心模块
Network Analysis Core Module

包含网络数据包分析的核心功能，包括：
- 数据包解析器基类和实现
- 网络分析器和算法
- 数据模型和结构
- 异常处理
- 工具函数

这个模块是整个分析工具的核心，提供了所有基础的分析能力。
"""

from .analyzer import NetworkAnalyzer, AnalysisResult
from .parser import PacketParser, PacketInfo
from .models import (
    Packet,
    ProtocolStats,
    TrafficStats,
    Anomaly,
    SecurityThreat,
    FileMetadata,
)
from .exceptions import (
    NetAnalysisError,
    ParseError,
    AnalysisError,
    ConfigError,
    ValidationError,
)
from .utils import (
    validate_file,
    detect_file_format,
    calculate_file_hash,
    format_bytes,
    format_duration,
)

# 导出所有公共接口
__all__ = [
    # 核心分析类
    "NetworkAnalyzer",
    "PacketParser",
    
    # 结果类
    "AnalysisResult",
    "PacketInfo",
    
    # 数据模型
    "Packet",
    "ProtocolStats", 
    "TrafficStats",
    "Anomaly",
    "SecurityThreat",
    "FileMetadata",
    
    # 异常类
    "NetAnalysisError",
    "ParseError",
    "AnalysisError", 
    "ConfigError",
    "ValidationError",
    
    # 工具函数
    "validate_file",
    "detect_file_format",
    "calculate_file_hash",
    "format_bytes",
    "format_duration",
]
