#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络分析数据模型
Network Analysis Data Models

定义了网络数据包分析中使用的所有数据结构和模型，
包括数据包、协议统计、流量分析、异常检测等核心数据类型。

这些模型使用dataclass和pydantic来确保类型安全和数据验证。
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import ipaddress
from pydantic import BaseModel, Field, validator


class ProtocolType(str, Enum):
    """协议类型枚举"""
    ETHERNET = "ethernet"
    ARP = "arp"
    VLAN = "vlan"
    IPV4 = "ipv4"
    IPV6 = "ipv6"
    ICMP = "icmp"
    ICMPV6 = "icmpv6"
    TCP = "tcp"
    UDP = "udp"
    SCTP = "sctp"
    HTTP = "http"
    HTTPS = "https"
    DNS = "dns"
    DHCP = "dhcp"
    SMTP = "smtp"
    POP3 = "pop3"
    IMAP = "imap"
    FTP = "ftp"
    SSH = "ssh"
    TELNET = "telnet"
    SNMP = "snmp"
    UNKNOWN = "unknown"


class PacketDirection(str, Enum):
    """数据包方向枚举"""
    INBOUND = "inbound"
    OUTBOUND = "outbound"
    INTERNAL = "internal"
    UNKNOWN = "unknown"


class AnomalyType(str, Enum):
    """异常类型枚举"""
    STATISTICAL = "statistical"
    RULE_BASED = "rule_based"
    BEHAVIORAL = "behavioral"
    SECURITY = "security"


class ThreatLevel(str, Enum):
    """威胁级别枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class Packet:
    """
    数据包基础数据结构
    
    包含单个网络数据包的所有基本信息，包括时间戳、地址、协议、大小等。
    """
    # 基础信息
    timestamp: datetime
    size: int
    
    # 网络层信息
    src_ip: Optional[str] = None
    dst_ip: Optional[str] = None
    ip_version: Optional[int] = None
    ttl: Optional[int] = None
    
    # 传输层信息
    src_port: Optional[int] = None
    dst_port: Optional[int] = None
    protocol: ProtocolType = ProtocolType.UNKNOWN
    
    # 数据链路层信息
    src_mac: Optional[str] = None
    dst_mac: Optional[str] = None
    
    # 应用层信息
    payload_size: int = 0
    payload: Optional[bytes] = None
    
    # 协议特定信息
    headers: Dict[str, Any] = field(default_factory=dict)
    flags: Dict[str, bool] = field(default_factory=dict)
    
    # 分析信息
    direction: PacketDirection = PacketDirection.UNKNOWN
    flow_id: Optional[str] = None
    session_id: Optional[str] = None
    
    def __post_init__(self):
        """数据包初始化后处理"""
        # 生成流ID（基于五元组）
        if self.src_ip and self.dst_ip and self.src_port and self.dst_port:
            flow_tuple = (
                min(self.src_ip, self.dst_ip),
                max(self.src_ip, self.dst_ip),
                min(self.src_port, self.dst_port),
                max(self.src_port, self.dst_port),
                self.protocol.value
            )
            self.flow_id = hash(flow_tuple)
    
    @property
    def is_tcp(self) -> bool:
        """是否为TCP协议"""
        return self.protocol == ProtocolType.TCP
    
    @property
    def is_udp(self) -> bool:
        """是否为UDP协议"""
        return self.protocol == ProtocolType.UDP
    
    @property
    def is_ipv4(self) -> bool:
        """是否为IPv4协议"""
        return self.ip_version == 4
    
    @property
    def is_ipv6(self) -> bool:
        """是否为IPv6协议"""
        return self.ip_version == 6


@dataclass
class ProtocolStats:
    """
    协议统计信息
    
    包含各种网络协议的使用统计数据。
    """
    # 协议分布统计
    protocol_counts: Dict[str, int] = field(default_factory=dict)
    protocol_bytes: Dict[str, int] = field(default_factory=dict)
    protocol_percentages: Dict[str, float] = field(default_factory=dict)
    
    # 端口统计
    top_src_ports: List[tuple] = field(default_factory=list)  # (port, count)
    top_dst_ports: List[tuple] = field(default_factory=list)
    
    # IP统计
    top_src_ips: List[tuple] = field(default_factory=list)  # (ip, count)
    top_dst_ips: List[tuple] = field(default_factory=list)
    
    # 总计信息
    total_packets: int = 0
    total_bytes: int = 0
    unique_flows: int = 0
    
    # 时间范围
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_seconds: float = 0.0


@dataclass
class TrafficStats:
    """
    流量统计信息
    
    包含网络流量的各种统计指标和时间序列数据。
    """
    # 基础统计
    total_packets: int = 0
    total_bytes: int = 0
    average_packet_size: float = 0.0
    
    # 速率统计
    packets_per_second: float = 0.0
    bytes_per_second: float = 0.0
    bits_per_second: float = 0.0
    
    # 连接统计
    total_connections: int = 0
    active_connections: int = 0
    failed_connections: int = 0
    
    # 时间序列数据
    packet_timeline: List[tuple] = field(default_factory=list)  # (timestamp, count)
    byte_timeline: List[tuple] = field(default_factory=list)
    connection_timeline: List[tuple] = field(default_factory=list)
    
    # 分布统计
    packet_size_distribution: Dict[str, int] = field(default_factory=dict)
    inter_arrival_times: List[float] = field(default_factory=list)
    
    # 地理位置统计（如果可用）
    geo_distribution: Dict[str, int] = field(default_factory=dict)

    # 新增：时间序列分析结果
    time_series_data: Dict[str, Any] = field(default_factory=dict)

    # 新增：Top N统计
    top_n_statistics: Dict[str, Any] = field(default_factory=dict)

    # 新增：流量模式识别结果
    traffic_patterns: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Anomaly:
    """
    异常检测结果

    表示检测到的网络异常情况。
    """
    # 基础信息
    anomaly_id: str
    anomaly_type: AnomalyType
    severity: ThreatLevel
    confidence: float  # 0.0 - 1.0

    # 描述信息
    title: str
    description: str

    # 时间信息
    detected_at: datetime

    # 可选字段
    details: Dict[str, Any] = field(default_factory=dict)
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    
    # 相关数据
    affected_packets: List[int] = field(default_factory=list)  # 数据包索引
    affected_flows: List[str] = field(default_factory=list)   # 流ID列表
    affected_hosts: List[str] = field(default_factory=list)   # 主机IP列表
    
    # 统计信息
    packet_count: int = 0
    byte_count: int = 0
    
    # 建议措施
    recommendations: List[str] = field(default_factory=list)


@dataclass
class SecurityThreat:
    """
    安全威胁信息

    表示检测到的安全威胁和攻击。
    """
    # 基础信息
    threat_id: str
    threat_type: str  # ddos, port_scan, malware, etc.
    severity: ThreatLevel
    confidence: float

    # 描述信息
    title: str
    description: str
    attack_vector: str

    # 时间信息
    detected_at: datetime

    # 可选字段
    source_ips: List[str] = field(default_factory=list)
    target_ips: List[str] = field(default_factory=list)
    source_ports: List[int] = field(default_factory=list)
    target_ports: List[int] = field(default_factory=list)
    first_seen: Optional[datetime] = None
    last_seen: Optional[datetime] = None
    
    # 统计信息
    attack_packets: int = 0
    attack_bytes: int = 0
    attack_duration: float = 0.0
    
    # 威胁指标
    indicators: Dict[str, Any] = field(default_factory=dict)
    
    # 缓解建议
    mitigation_steps: List[str] = field(default_factory=list)


@dataclass
class FileMetadata:
    """
    文件元数据信息
    
    包含分析文件的基本信息和统计数据。
    """
    # 文件基础信息
    filename: str
    file_path: str
    file_size: int
    file_hash: str
    file_format: str
    
    # 时间信息
    created_at: datetime
    modified_at: Optional[datetime] = None
    analyzed_at: Optional[datetime] = None
    
    # 数据包信息
    total_packets: int = 0
    packet_size_range: tuple = (0, 0)  # (min, max)
    time_range: tuple = (None, None)   # (start, end)
    
    # 网络信息
    unique_ips: int = 0
    unique_ports: int = 0
    protocols_detected: List[str] = field(default_factory=list)
    
    # 质量信息
    corrupted_packets: int = 0
    truncated_packets: int = 0
    duplicate_packets: int = 0
    
    # 额外信息
    capture_interface: Optional[str] = None
    capture_filter: Optional[str] = None
    comments: List[str] = field(default_factory=list)


class PacketInfo(BaseModel):
    """
    数据包信息Pydantic模型
    
    用于API接口的数据包信息传输。
    """
    timestamp: datetime
    size: int
    src_ip: Optional[str] = None
    dst_ip: Optional[str] = None
    src_port: Optional[int] = None
    dst_port: Optional[int] = None
    protocol: str
    direction: str = "unknown"
    
    @validator('src_ip', 'dst_ip')
    def validate_ip_address(cls, v):
        """验证IP地址格式"""
        if v is not None:
            try:
                ipaddress.ip_address(v)
            except ValueError:
                raise ValueError(f"无效的IP地址: {v}")
        return v
    
    @validator('src_port', 'dst_port')
    def validate_port(cls, v):
        """验证端口号范围"""
        if v is not None and not (0 <= v <= 65535):
            raise ValueError(f"端口号必须在0-65535范围内: {v}")
        return v


class AnalysisRequest(BaseModel):
    """
    分析请求模型
    
    用于API接口的分析请求参数。
    """
    file_id: str
    analysis_types: List[str] = Field(default=["protocol", "traffic"])
    options: Dict[str, Any] = Field(default_factory=dict)
    
    @validator('analysis_types')
    def validate_analysis_types(cls, v):
        """验证分析类型"""
        valid_types = ["protocol", "traffic", "anomaly", "security", "ai"]
        for analysis_type in v:
            if analysis_type not in valid_types:
                raise ValueError(f"无效的分析类型: {analysis_type}")
        return v


class AnalysisResult(BaseModel):
    """
    分析结果模型
    
    用于API接口的分析结果传输。
    """
    analysis_id: str
    file_metadata: Dict[str, Any]
    protocol_stats: Optional[Dict[str, Any]] = None
    traffic_stats: Optional[Dict[str, Any]] = None
    anomalies: List[Dict[str, Any]] = Field(default_factory=list)
    security_threats: List[Dict[str, Any]] = Field(default_factory=list)
    ai_insights: List[Dict[str, Any]] = Field(default_factory=list)
    
    # 分析元信息
    analysis_duration: float = 0.0
    analyzed_at: datetime = Field(default_factory=datetime.now)
    status: str = "completed"
    error_message: Optional[str] = None
