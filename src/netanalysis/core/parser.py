#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络数据包解析器基类
Network Packet Parser Base Class

定义了数据包解析器的抽象基类和通用接口，
为不同格式的解析器提供统一的API。

支持的解析模式：
- 流式解析：适用于大文件，内存占用低
- 批量解析：适用于小文件，处理速度快
- 增量解析：支持实时数据流处理
"""

import os
import hashlib
import logging
from abc import ABC, abstractmethod
from typing import Iterator, List, Optional, Dict, Any, Union
from pathlib import Path
from datetime import datetime

from .models import Packet, FileMetadata, PacketInfo, ProtocolType
from .exceptions import (
    ParseError,
    FileFormatError,
    CorruptedFileError,
    UnsupportedFormatError,
    ValidationError
)
from .utils import validate_file, detect_file_format, calculate_file_hash

logger = logging.getLogger(__name__)


class PacketParser(ABC):
    """
    数据包解析器抽象基类
    
    定义了所有数据包解析器必须实现的接口方法。
    子类需要实现具体的解析逻辑。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化解析器
        
        Args:
            config: 解析器配置参数
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # 解析统计信息
        self.stats = {
            "total_packets": 0,
            "parsed_packets": 0,
            "failed_packets": 0,
            "start_time": None,
            "end_time": None,
        }
        
        # 支持的文件扩展名
        self.supported_extensions: List[str] = []
        
        # 解析选项
        self.chunk_size = self.config.get("chunk_size", 1024 * 1024)  # 1MB
        self.max_packets = self.config.get("max_packets", None)
        self.filter_invalid = self.config.get("filter_invalid", True)
        self.lazy_loading = self.config.get("lazy_loading", True)
    
    @abstractmethod
    def can_parse(self, file_path: str) -> bool:
        """
        检查是否可以解析指定文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否支持解析该文件
        """
        pass
    
    @abstractmethod
    def parse_file(self, file_path: str) -> Iterator[Packet]:
        """
        解析文件并返回数据包迭代器
        
        Args:
            file_path: 文件路径
            
        Yields:
            Packet: 解析出的数据包对象
            
        Raises:
            ParseError: 解析错误
            FileFormatError: 文件格式错误
            CorruptedFileError: 文件损坏
        """
        pass
    
    @abstractmethod
    def get_file_metadata(self, file_path: str) -> FileMetadata:
        """
        获取文件元数据信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            FileMetadata: 文件元数据对象
            
        Raises:
            ParseError: 解析错误
        """
        pass
    
    def parse_packets(
        self,
        file_path: str,
        max_packets: Optional[int] = None,
        skip_invalid: bool = True
    ) -> List[Packet]:
        """
        解析文件并返回数据包列表
        
        Args:
            file_path: 文件路径
            max_packets: 最大解析数据包数量
            skip_invalid: 是否跳过无效数据包
            
        Returns:
            List[Packet]: 数据包列表
        """
        packets = []
        count = 0
        max_count = max_packets or self.max_packets
        
        try:
            for packet in self.parse_file(file_path):
                if max_count and count >= max_count:
                    break
                
                if skip_invalid and not self._validate_packet(packet):
                    self.stats["failed_packets"] += 1
                    continue
                
                packets.append(packet)
                count += 1
                self.stats["parsed_packets"] += 1
                
                # 定期记录进度
                if count % 10000 == 0:
                    self.logger.debug(f"已解析 {count} 个数据包")
        
        except Exception as e:
            self.logger.error(f"解析文件时发生错误: {e}")
            raise ParseError(f"解析文件失败: {e}", file_path=file_path, cause=e)
        
        self.logger.info(f"解析完成，共解析 {len(packets)} 个数据包")
        return packets
    
    def validate_file(self, file_path: str) -> bool:
        """
        验证文件是否有效
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 文件是否有效
        """
        try:
            # 基础文件验证
            if not validate_file(file_path):
                return False
            
            # 格式特定验证
            return self.can_parse(file_path)
        
        except Exception as e:
            self.logger.warning(f"文件验证失败: {e}")
            return False
    
    def get_parse_stats(self) -> Dict[str, Any]:
        """
        获取解析统计信息
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        stats = self.stats.copy()
        
        if stats["start_time"] and stats["end_time"]:
            duration = (stats["end_time"] - stats["start_time"]).total_seconds()
            stats["duration_seconds"] = duration
            
            if duration > 0:
                stats["packets_per_second"] = stats["parsed_packets"] / duration
        
        return stats
    
    def reset_stats(self):
        """重置解析统计信息"""
        self.stats = {
            "total_packets": 0,
            "parsed_packets": 0,
            "failed_packets": 0,
            "start_time": None,
            "end_time": None,
        }
    
    def _validate_packet(self, packet: Packet) -> bool:
        """
        验证数据包是否有效
        
        Args:
            packet: 数据包对象
            
        Returns:
            bool: 数据包是否有效
        """
        try:
            # 基础验证
            if not isinstance(packet, Packet):
                return False
            
            if packet.size <= 0:
                return False
            
            if not packet.timestamp:
                return False
            
            # IP地址验证
            if packet.src_ip:
                import ipaddress
                ipaddress.ip_address(packet.src_ip)
            
            if packet.dst_ip:
                import ipaddress
                ipaddress.ip_address(packet.dst_ip)
            
            # 端口验证
            if packet.src_port is not None and not (0 <= packet.src_port <= 65535):
                return False
            
            if packet.dst_port is not None and not (0 <= packet.dst_port <= 65535):
                return False
            
            return True
        
        except Exception:
            return False
    
    def _start_parsing(self):
        """开始解析时的初始化"""
        self.stats["start_time"] = datetime.now()
        self.logger.info("开始解析数据包文件")
    
    def _end_parsing(self):
        """结束解析时的清理"""
        self.stats["end_time"] = datetime.now()
        stats = self.get_parse_stats()
        self.logger.info(
            f"解析完成 - 总计: {stats['total_packets']}, "
            f"成功: {stats['parsed_packets']}, "
            f"失败: {stats['failed_packets']}, "
            f"耗时: {stats.get('duration_seconds', 0):.2f}秒"
        )
    
    def _create_file_metadata(self, file_path: str) -> FileMetadata:
        """
        创建基础文件元数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            FileMetadata: 文件元数据对象
        """
        path = Path(file_path)
        stat = path.stat()
        
        return FileMetadata(
            filename=path.name,
            file_path=str(path.absolute()),
            file_size=stat.st_size,
            file_hash=calculate_file_hash(file_path),
            file_format=detect_file_format(file_path),
            created_at=datetime.fromtimestamp(stat.st_ctime),
            modified_at=datetime.fromtimestamp(stat.st_mtime),
            analyzed_at=datetime.now(),
        )
    
    def __str__(self) -> str:
        """返回解析器的字符串表示"""
        return f"{self.__class__.__name__}(extensions={self.supported_extensions})"
    
    def __repr__(self) -> str:
        """返回解析器的详细表示"""
        return (
            f"{self.__class__.__name__}("
            f"extensions={self.supported_extensions}, "
            f"config={self.config})"
        )


class ParserRegistry:
    """
    解析器注册表
    
    管理所有可用的数据包解析器，提供自动选择和注册功能。
    """
    
    def __init__(self):
        """初始化解析器注册表"""
        self._parsers: Dict[str, PacketParser] = {}
        self.logger = logging.getLogger(f"{__name__}.ParserRegistry")
    
    def register(self, name: str, parser_class: type, config: Optional[Dict] = None):
        """
        注册解析器
        
        Args:
            name: 解析器名称
            parser_class: 解析器类
            config: 解析器配置
        """
        if not issubclass(parser_class, PacketParser):
            raise ValueError(f"解析器类必须继承自PacketParser: {parser_class}")
        
        parser = parser_class(config)
        self._parsers[name] = parser
        self.logger.info(f"注册解析器: {name} -> {parser_class.__name__}")
    
    def get_parser(self, name: str) -> Optional[PacketParser]:
        """
        获取指定名称的解析器
        
        Args:
            name: 解析器名称
            
        Returns:
            Optional[PacketParser]: 解析器实例
        """
        return self._parsers.get(name)
    
    def get_parser_for_file(self, file_path: str) -> Optional[PacketParser]:
        """
        根据文件自动选择合适的解析器
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[PacketParser]: 合适的解析器实例
        """
        for parser in self._parsers.values():
            if parser.can_parse(file_path):
                self.logger.debug(f"为文件 {file_path} 选择解析器: {parser}")
                return parser
        
        self.logger.warning(f"未找到适合文件 {file_path} 的解析器")
        return None
    
    def list_parsers(self) -> List[str]:
        """
        列出所有已注册的解析器
        
        Returns:
            List[str]: 解析器名称列表
        """
        return list(self._parsers.keys())
    
    def get_supported_formats(self) -> List[str]:
        """
        获取所有支持的文件格式
        
        Returns:
            List[str]: 支持的文件扩展名列表
        """
        formats = set()
        for parser in self._parsers.values():
            formats.update(parser.supported_extensions)
        return sorted(list(formats))


# 全局解析器注册表实例
parser_registry = ParserRegistry()
