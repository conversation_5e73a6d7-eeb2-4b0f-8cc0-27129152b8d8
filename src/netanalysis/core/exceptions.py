#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络分析异常处理模块
Network Analysis Exception Handling Module

定义了网络数据包分析工具中使用的所有异常类，
提供了详细的错误信息和错误处理机制。

异常层次结构：
NetAnalysisError (基础异常)
├── ParseError (解析相关异常)
│   ├── FileFormatError (文件格式错误)
│   ├── CorruptedFileError (文件损坏错误)
│   └── UnsupportedFormatError (不支持的格式)
├── AnalysisError (分析相关异常)
│   ├── InsufficientDataError (数据不足错误)
│   ├── AnalysisTimeoutError (分析超时错误)
│   └── MemoryLimitError (内存限制错误)
├── ConfigError (配置相关异常)
│   ├── InvalidConfigError (无效配置错误)
│   └── MissingConfigError (缺失配置错误)
├── ValidationError (验证相关异常)
├── AIServiceError (AI服务相关异常)
└── StorageError (存储相关异常)
"""

from typing import Optional, Dict, Any
import traceback
from datetime import datetime


class NetAnalysisError(Exception):
    """
    网络分析工具基础异常类
    
    所有其他异常类的基类，提供了通用的错误处理功能。
    """
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误代码
            details: 错误详细信息
            cause: 引起此异常的原始异常
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.cause = cause
        self.timestamp = datetime.now()
        self.traceback_str = traceback.format_exc() if cause else None
    
    def to_dict(self) -> Dict[str, Any]:
        """将异常转换为字典格式"""
        return {
            "error_type": self.__class__.__name__,
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
            "traceback": self.traceback_str,
        }
    
    def __str__(self) -> str:
        """返回异常的字符串表示"""
        base_msg = f"[{self.error_code}] {self.message}"
        if self.details:
            details_str = ", ".join(f"{k}={v}" for k, v in self.details.items())
            base_msg += f" ({details_str})"
        return base_msg


class ParseError(NetAnalysisError):
    """数据包解析相关异常"""
    
    def __init__(
        self,
        message: str,
        file_path: Optional[str] = None,
        line_number: Optional[int] = None,
        **kwargs
    ):
        details = kwargs.get("details", {})
        if file_path:
            details["file_path"] = file_path
        if line_number:
            details["line_number"] = line_number
        kwargs["details"] = details
        super().__init__(message, **kwargs)


class FileFormatError(ParseError):
    """文件格式错误异常"""
    
    def __init__(
        self,
        message: str,
        expected_format: Optional[str] = None,
        actual_format: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get("details", {})
        if expected_format:
            details["expected_format"] = expected_format
        if actual_format:
            details["actual_format"] = actual_format
        kwargs["details"] = details
        super().__init__(message, **kwargs)


class CorruptedFileError(ParseError):
    """文件损坏错误异常"""
    
    def __init__(
        self,
        message: str,
        corruption_type: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get("details", {})
        if corruption_type:
            details["corruption_type"] = corruption_type
        kwargs["details"] = details
        super().__init__(message, **kwargs)


class UnsupportedFormatError(ParseError):
    """不支持的格式异常"""
    
    def __init__(
        self,
        message: str,
        format_name: Optional[str] = None,
        supported_formats: Optional[list] = None,
        **kwargs
    ):
        details = kwargs.get("details", {})
        if format_name:
            details["format_name"] = format_name
        if supported_formats:
            details["supported_formats"] = supported_formats
        kwargs["details"] = details
        super().__init__(message, **kwargs)


class AnalysisError(NetAnalysisError):
    """网络分析相关异常"""
    
    def __init__(
        self,
        message: str,
        analysis_type: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get("details", {})
        if analysis_type:
            details["analysis_type"] = analysis_type
        kwargs["details"] = details
        super().__init__(message, **kwargs)


class InsufficientDataError(AnalysisError):
    """数据不足错误异常"""
    
    def __init__(
        self,
        message: str,
        required_packets: Optional[int] = None,
        available_packets: Optional[int] = None,
        **kwargs
    ):
        details = kwargs.get("details", {})
        if required_packets:
            details["required_packets"] = required_packets
        if available_packets:
            details["available_packets"] = available_packets
        kwargs["details"] = details
        super().__init__(message, **kwargs)


class AnalysisTimeoutError(AnalysisError):
    """分析超时错误异常"""
    
    def __init__(
        self,
        message: str,
        timeout_seconds: Optional[int] = None,
        **kwargs
    ):
        details = kwargs.get("details", {})
        if timeout_seconds:
            details["timeout_seconds"] = timeout_seconds
        kwargs["details"] = details
        super().__init__(message, **kwargs)


class MemoryLimitError(AnalysisError):
    """内存限制错误异常"""
    
    def __init__(
        self,
        message: str,
        memory_limit: Optional[int] = None,
        memory_used: Optional[int] = None,
        **kwargs
    ):
        details = kwargs.get("details", {})
        if memory_limit:
            details["memory_limit"] = memory_limit
        if memory_used:
            details["memory_used"] = memory_used
        kwargs["details"] = details
        super().__init__(message, **kwargs)


class ConfigError(NetAnalysisError):
    """配置相关异常"""
    
    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        config_file: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get("details", {})
        if config_key:
            details["config_key"] = config_key
        if config_file:
            details["config_file"] = config_file
        kwargs["details"] = details
        super().__init__(message, **kwargs)


class InvalidConfigError(ConfigError):
    """无效配置错误异常"""
    
    def __init__(
        self,
        message: str,
        invalid_value: Optional[Any] = None,
        expected_type: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get("details", {})
        if invalid_value is not None:
            details["invalid_value"] = str(invalid_value)
        if expected_type:
            details["expected_type"] = expected_type
        kwargs["details"] = details
        super().__init__(message, **kwargs)


class MissingConfigError(ConfigError):
    """缺失配置错误异常"""
    pass


class ValidationError(NetAnalysisError):
    """验证相关异常"""
    
    def __init__(
        self,
        message: str,
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        **kwargs
    ):
        details = kwargs.get("details", {})
        if field_name:
            details["field_name"] = field_name
        if field_value is not None:
            details["field_value"] = str(field_value)
        kwargs["details"] = details
        super().__init__(message, **kwargs)


class AIServiceError(NetAnalysisError):
    """AI服务相关异常"""
    
    def __init__(
        self,
        message: str,
        service_name: Optional[str] = None,
        status_code: Optional[int] = None,
        **kwargs
    ):
        details = kwargs.get("details", {})
        if service_name:
            details["service_name"] = service_name
        if status_code:
            details["status_code"] = status_code
        kwargs["details"] = details
        super().__init__(message, **kwargs)


class StorageError(NetAnalysisError):
    """存储相关异常"""
    
    def __init__(
        self,
        message: str,
        storage_path: Optional[str] = None,
        operation: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get("details", {})
        if storage_path:
            details["storage_path"] = storage_path
        if operation:
            details["operation"] = operation
        kwargs["details"] = details
        super().__init__(message, **kwargs)


# 异常处理工具函数
def handle_exception(
    exception: Exception,
    context: Optional[Dict[str, Any]] = None,
    reraise: bool = True
) -> Optional[NetAnalysisError]:
    """
    统一异常处理函数
    
    Args:
        exception: 原始异常
        context: 异常上下文信息
        reraise: 是否重新抛出异常
    
    Returns:
        处理后的NetAnalysisError异常（如果reraise=False）
    
    Raises:
        NetAnalysisError: 处理后的异常（如果reraise=True）
    """
    # 如果已经是NetAnalysisError，直接处理
    if isinstance(exception, NetAnalysisError):
        if context:
            exception.details.update(context)
        if reraise:
            raise exception
        return exception
    
    # 将其他异常转换为NetAnalysisError
    error_msg = str(exception)
    details = context or {}
    details["original_exception"] = exception.__class__.__name__
    
    net_error = NetAnalysisError(
        message=f"未处理的异常: {error_msg}",
        error_code="UNHANDLED_EXCEPTION",
        details=details,
        cause=exception
    )
    
    if reraise:
        raise net_error
    return net_error


def format_exception_for_user(exception: NetAnalysisError) -> str:
    """
    为用户格式化异常信息
    
    Args:
        exception: NetAnalysisError异常
    
    Returns:
        用户友好的错误消息
    """
    if isinstance(exception, FileFormatError):
        return f"文件格式错误: {exception.message}"
    elif isinstance(exception, CorruptedFileError):
        return f"文件损坏: {exception.message}"
    elif isinstance(exception, UnsupportedFormatError):
        return f"不支持的文件格式: {exception.message}"
    elif isinstance(exception, InsufficientDataError):
        return f"数据不足: {exception.message}"
    elif isinstance(exception, AnalysisTimeoutError):
        return f"分析超时: {exception.message}"
    elif isinstance(exception, MemoryLimitError):
        return f"内存不足: {exception.message}"
    elif isinstance(exception, ConfigError):
        return f"配置错误: {exception.message}"
    elif isinstance(exception, ValidationError):
        return f"输入验证失败: {exception.message}"
    elif isinstance(exception, AIServiceError):
        return f"AI服务错误: {exception.message}"
    elif isinstance(exception, StorageError):
        return f"存储错误: {exception.message}"
    else:
        return f"系统错误: {exception.message}"
