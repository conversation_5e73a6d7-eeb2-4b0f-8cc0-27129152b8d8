#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web界面模块
Web Interface Module

完善Web用户界面，集成所有分析和可视化功能。
包含FastAPI框架、文件上传、分析结果展示等功能。
"""

from .api import app, create_app
from .middleware import setup_middleware
from .auth import setup_auth

# 检查依赖库可用性
try:
    import fastapi
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False

try:
    import uvicorn
    UVICORN_AVAILABLE = True
except ImportError:
    UVICORN_AVAILABLE = False

__all__ = [
    'app',
    'create_app',
    'setup_middleware',
    'setup_auth',
    'FASTAPI_AVAILABLE',
    'UVICORN_AVAILABLE'
]
