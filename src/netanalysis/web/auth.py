#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证授权模块
Authentication and Authorization Module

提供用户认证、授权、会话管理等安全功能。
支持JWT令牌、API密钥等多种认证方式。
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import hashlib
import secrets
import jwt

try:
    from fastapi import FastAPI, HTTPException, Depends, status
    from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials, APIKeyHeader
    from pydantic import BaseModel
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    logging.warning("FastAPI库未安装，认证功能将受限")


# Pydantic模型
class User(BaseModel):
    """用户模型"""
    username: str
    email: Optional[str] = None
    roles: List[str] = []
    is_active: bool = True
    created_at: datetime = datetime.now()


class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str
    password: str


class TokenResponse(BaseModel):
    """令牌响应模型"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: User


class APIKeyRequest(BaseModel):
    """API密钥请求模型"""
    name: str
    description: Optional[str] = None
    expires_days: Optional[int] = 30


class APIKeyResponse(BaseModel):
    """API密钥响应模型"""
    key_id: str
    api_key: str
    name: str
    expires_at: datetime


# 全局变量
security_bearer = HTTPBearer() if FASTAPI_AVAILABLE else None
security_api_key = APIKeyHeader(name="X-API-Key") if FASTAPI_AVAILABLE else None


class AuthManager:
    """
    认证管理器
    
    提供用户认证、JWT令牌管理、API密钥管理等功能。
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化认证管理器
        
        Args:
            config: 认证配置，包括：
                - secret_key: JWT密钥
                - algorithm: JWT算法（默认HS256）
                - access_token_expire_minutes: 访问令牌过期时间（默认30分钟）
                - enable_api_key: 是否启用API密钥认证（默认True）
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.AuthManager")
        
        # JWT配置
        self.secret_key = self.config.get('secret_key', self._generate_secret_key())
        self.algorithm = self.config.get('algorithm', 'HS256')
        self.access_token_expire_minutes = self.config.get('access_token_expire_minutes', 30)
        
        # API密钥配置
        self.enable_api_key = self.config.get('enable_api_key', True)
        
        # 用户存储（简单实现，生产环境应使用数据库）
        self.users = {
            "admin": {
                "username": "admin",
                "password_hash": self._hash_password("admin123"),
                "email": "<EMAIL>",
                "roles": ["admin", "user"],
                "is_active": True
            },
            "user": {
                "username": "user",
                "password_hash": self._hash_password("user123"),
                "email": "<EMAIL>",
                "roles": ["user"],
                "is_active": True
            }
        }
        
        # API密钥存储
        self.api_keys = {}
        
        self.logger.info("认证管理器初始化完成")
    
    def _generate_secret_key(self) -> str:
        """生成随机密钥"""
        return secrets.token_urlsafe(32)
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        return self._hash_password(password) == password_hash
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """
        用户认证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Optional[User]: 认证成功返回用户对象，失败返回None
        """
        user_data = self.users.get(username)
        if not user_data:
            return None
        
        if not user_data["is_active"]:
            return None
        
        if not self._verify_password(password, user_data["password_hash"]):
            return None
        
        return User(
            username=user_data["username"],
            email=user_data["email"],
            roles=user_data["roles"],
            is_active=user_data["is_active"]
        )
    
    def create_access_token(self, user: User) -> str:
        """
        创建访问令牌
        
        Args:
            user: 用户对象
            
        Returns:
            str: JWT访问令牌
        """
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        payload = {
            "sub": user.username,
            "exp": expire,
            "iat": datetime.utcnow(),
            "roles": user.roles
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str) -> Optional[User]:
        """
        验证访问令牌
        
        Args:
            token: JWT令牌
            
        Returns:
            Optional[User]: 验证成功返回用户对象，失败返回None
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            username = payload.get("sub")
            
            if username is None:
                return None
            
            user_data = self.users.get(username)
            if not user_data or not user_data["is_active"]:
                return None
            
            return User(
                username=user_data["username"],
                email=user_data["email"],
                roles=user_data["roles"],
                is_active=user_data["is_active"]
            )
            
        except jwt.PyJWTError:
            return None
    
    def create_api_key(self, name: str, description: str = None, 
                      expires_days: int = 30) -> APIKeyResponse:
        """
        创建API密钥
        
        Args:
            name: 密钥名称
            description: 密钥描述
            expires_days: 过期天数
            
        Returns:
            APIKeyResponse: API密钥响应
        """
        key_id = secrets.token_urlsafe(8)
        api_key = f"nak_{secrets.token_urlsafe(32)}"  # nak = network analysis key
        expires_at = datetime.now() + timedelta(days=expires_days)
        
        self.api_keys[api_key] = {
            "key_id": key_id,
            "name": name,
            "description": description,
            "expires_at": expires_at,
            "created_at": datetime.now(),
            "is_active": True
        }
        
        self.logger.info(f"创建API密钥: {name} (ID: {key_id})")
        
        return APIKeyResponse(
            key_id=key_id,
            api_key=api_key,
            name=name,
            expires_at=expires_at
        )
    
    def verify_api_key(self, api_key: str) -> bool:
        """
        验证API密钥
        
        Args:
            api_key: API密钥
            
        Returns:
            bool: 验证结果
        """
        key_data = self.api_keys.get(api_key)
        if not key_data:
            return False
        
        if not key_data["is_active"]:
            return False
        
        if datetime.now() > key_data["expires_at"]:
            return False
        
        return True
    
    def revoke_api_key(self, api_key: str) -> bool:
        """
        撤销API密钥
        
        Args:
            api_key: API密钥
            
        Returns:
            bool: 撤销结果
        """
        if api_key in self.api_keys:
            self.api_keys[api_key]["is_active"] = False
            self.logger.info(f"撤销API密钥: {self.api_keys[api_key]['name']}")
            return True
        return False


# 全局认证管理器实例
auth_manager = None


def setup_auth(app: 'FastAPI', config: Dict[str, Any] = None) -> None:
    """
    设置认证功能
    
    Args:
        app: FastAPI应用实例
        config: 认证配置
    """
    if not FASTAPI_AVAILABLE:
        return
    
    global auth_manager
    auth_manager = AuthManager(config)
    
    # 依赖函数
    async def get_current_user_from_token(
        credentials: HTTPAuthorizationCredentials = Depends(security_bearer)
    ) -> User:
        """从JWT令牌获取当前用户"""
        user = auth_manager.verify_token(credentials.credentials)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的访问令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return user
    
    async def get_current_user_from_api_key(
        api_key: str = Depends(security_api_key)
    ) -> bool:
        """从API密钥验证用户"""
        if not auth_manager.verify_api_key(api_key):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的API密钥",
            )
        return True
    
    async def require_admin_role(
        current_user: User = Depends(get_current_user_from_token)
    ) -> User:
        """要求管理员角色"""
        if "admin" not in current_user.roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要管理员权限"
            )
        return current_user
    
    # 认证路由
    @app.post("/auth/login", response_model=TokenResponse)
    async def login(request: LoginRequest):
        """用户登录"""
        user = auth_manager.authenticate_user(request.username, request.password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        access_token = auth_manager.create_access_token(user)
        
        return TokenResponse(
            access_token=access_token,
            expires_in=auth_manager.access_token_expire_minutes * 60,
            user=user
        )
    
    @app.post("/auth/api-keys", response_model=APIKeyResponse)
    async def create_api_key(
        request: APIKeyRequest,
        current_user: User = Depends(require_admin_role)
    ):
        """创建API密钥（需要管理员权限）"""
        return auth_manager.create_api_key(
            name=request.name,
            description=request.description,
            expires_days=request.expires_days or 30
        )
    
    @app.delete("/auth/api-keys/{api_key}")
    async def revoke_api_key(
        api_key: str,
        current_user: User = Depends(require_admin_role)
    ):
        """撤销API密钥（需要管理员权限）"""
        success = auth_manager.revoke_api_key(api_key)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API密钥不存在"
            )
        return {"message": "API密钥已撤销"}
    
    @app.get("/auth/me", response_model=User)
    async def get_current_user(current_user: User = Depends(get_current_user_from_token)):
        """获取当前用户信息"""
        return current_user
    
    # 将依赖函数添加到应用中，供其他路由使用
    app.dependency_overrides.update({
        "get_current_user": get_current_user_from_token,
        "get_api_key_auth": get_current_user_from_api_key,
        "require_admin": require_admin_role
    })
    
    logging.info("认证功能设置完成")
