<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络数据包分析工具 - 主界面</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        .upload-area.dragover {
            border-color: #667eea;
            background-color: #e3f2fd;
        }
        .nav-link {
            color: rgba(255,255,255,0.8) !important;
            transition: all 0.3s ease;
        }
        .nav-link:hover, .nav-link.active {
            color: white !important;
            background-color: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .loading {
            display: none;
        }
        .progress-container {
            display: none;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <h4><i class="fas fa-network-wired"></i> 网络分析</h4>
                    <small>Network Packet Analyzer</small>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link active" href="#" data-section="dashboard">
                        <i class="fas fa-tachometer-alt me-2"></i> 仪表板
                    </a>
                    <a class="nav-link" href="#" data-section="upload">
                        <i class="fas fa-upload me-2"></i> 文件上传
                    </a>
                    <a class="nav-link" href="#" data-section="analysis">
                        <i class="fas fa-chart-line me-2"></i> 流量分析
                    </a>
                    <a class="nav-link" href="#" data-section="security">
                        <i class="fas fa-shield-alt me-2"></i> 安全检测
                    </a>
                    <a class="nav-link" href="#" data-section="visualization">
                        <i class="fas fa-chart-pie me-2"></i> 可视化
                    </a>
                    <a class="nav-link" href="#" data-section="ai">
                        <i class="fas fa-robot me-2"></i> AI分析
                    </a>
                    <a class="nav-link" href="#" data-section="settings">
                        <i class="fas fa-cog me-2"></i> 设置
                    </a>
                </nav>
                
                <div class="mt-auto pt-4">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i> 版本 1.2.0<br>
                        <i class="fas fa-clock"></i> <span id="current-time"></span>
                    </small>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="col-md-9 col-lg-10 main-content p-4">
                <!-- 仪表板部分 -->
                <div id="dashboard-section" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-tachometer-alt"></i> 网络分析仪表板</h2>
                        <button class="btn btn-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                    
                    <!-- 统计卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card stat-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-network-wired fa-2x mb-2"></i>
                                    <h4 id="total-packets">0</h4>
                                    <small>总数据包</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-tachometer-alt fa-2x mb-2"></i>
                                    <h4 id="packet-rate">0</h4>
                                    <small>包/秒</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-wifi fa-2x mb-2"></i>
                                    <h4 id="bandwidth">0</h4>
                                    <small>Mbps</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                    <h4 id="anomalies">0</h4>
                                    <small>异常检测</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 图表区域 -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="chart-container">
                                <h5><i class="fas fa-chart-line"></i> 流量时间线</h5>
                                <div id="traffic-timeline" style="height: 400px;"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="chart-container">
                                <h5><i class="fas fa-chart-pie"></i> 协议分布</h5>
                                <div id="protocol-distribution" style="height: 400px;"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="chart-container">
                                <h5><i class="fas fa-project-diagram"></i> 网络拓扑</h5>
                                <div id="network-topology" style="height: 500px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 文件上传部分 -->
                <div id="upload-section" class="content-section" style="display: none;">
                    <h2><i class="fas fa-upload"></i> 文件上传</h2>
                    <p class="text-muted">支持PCAP、PCAPNG等网络数据包文件格式</p>
                    
                    <div class="upload-area" id="upload-area">
                        <i class="fas fa-cloud-upload-alt fa-3x mb-3 text-muted"></i>
                        <h5>拖拽文件到此处或点击选择文件</h5>
                        <p class="text-muted">支持 .pcap, .pcapng, .cap 格式</p>
                        <input type="file" id="file-input" accept=".pcap,.pcapng,.cap" style="display: none;">
                        <button class="btn btn-primary" onclick="document.getElementById('file-input').click()">
                            <i class="fas fa-folder-open"></i> 选择文件
                        </button>
                    </div>
                    
                    <div class="progress-container">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted mt-2">上传进度</small>
                    </div>
                    
                    <div class="mt-4">
                        <h5>分析选项</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable-ai" checked>
                                    <label class="form-check-label" for="enable-ai">
                                        启用AI分析
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable-visualization" checked>
                                    <label class="form-check-label" for="enable-visualization">
                                        生成可视化图表
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable-security" checked>
                                    <label class="form-check-label" for="enable-security">
                                        安全威胁检测
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="analysis-type" class="form-label">分析类型</label>
                                    <select class="form-select" id="analysis-type">
                                        <option value="comprehensive">综合分析</option>
                                        <option value="security">安全分析</option>
                                        <option value="performance">性能分析</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 其他部分占位符 -->
                <div id="analysis-section" class="content-section" style="display: none;">
                    <h2><i class="fas fa-chart-line"></i> 流量分析</h2>
                    <p>详细的流量分析功能正在开发中...</p>
                </div>
                
                <div id="security-section" class="content-section" style="display: none;">
                    <h2><i class="fas fa-shield-alt"></i> 安全检测</h2>
                    <p>安全威胁检测功能正在开发中...</p>
                </div>
                
                <div id="visualization-section" class="content-section" style="display: none;">
                    <h2><i class="fas fa-chart-pie"></i> 可视化</h2>
                    <p>高级可视化功能正在开发中...</p>
                </div>
                
                <div id="ai-section" class="content-section" style="display: none;">
                    <h2><i class="fas fa-robot"></i> AI分析</h2>
                    <p>AI智能分析功能正在开发中...</p>
                </div>
                
                <div id="settings-section" class="content-section" style="display: none;">
                    <h2><i class="fas fa-cog"></i> 设置</h2>
                    <p>系统设置功能正在开发中...</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JavaScript -->
    <script>
        // 全局变量
        let currentData = null;
        let charts = {};

        // 初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
        });

        function initializeApp() {
            // 设置导航事件
            setupNavigation();

            // 设置文件上传
            setupFileUpload();

            // 加载初始数据
            loadDashboardData();
        }

        function setupNavigation() {
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 移除所有active类
                    navLinks.forEach(l => l.classList.remove('active'));

                    // 添加active类到当前链接
                    this.classList.add('active');

                    // 隐藏所有内容区域
                    const sections = document.querySelectorAll('.content-section');
                    sections.forEach(section => section.style.display = 'none');

                    // 显示对应的内容区域
                    const sectionId = this.getAttribute('data-section') + '-section';
                    const targetSection = document.getElementById(sectionId);
                    if (targetSection) {
                        targetSection.style.display = 'block';
                    }
                });
            });
        }

        function setupFileUpload() {
            const uploadArea = document.getElementById('upload-area');
            const fileInput = document.getElementById('file-input');

            // 拖拽事件
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileUpload(files[0]);
                }
            });

            // 文件选择事件
            fileInput.addEventListener('change', function(e) {
                if (this.files.length > 0) {
                    handleFileUpload(this.files[0]);
                }
            });
        }

        function handleFileUpload(file) {
            console.log('上传文件:', file.name);

            // 显示进度条
            const progressContainer = document.querySelector('.progress-container');
            const progressBar = document.querySelector('.progress-bar');
            progressContainer.style.display = 'block';

            // 创建FormData
            const formData = new FormData();
            formData.append('file', file);
            formData.append('analysis_type', document.getElementById('analysis-type').value);
            formData.append('enable_ai', document.getElementById('enable-ai').checked);
            formData.append('enable_visualization', document.getElementById('enable-visualization').checked);
            formData.append('enable_security', document.getElementById('enable-security').checked);

            // 上传文件
            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log('上传成功:', data);
                progressBar.style.width = '100%';

                // 更新仪表板数据
                if (data.success) {
                    currentData = data;
                    updateDashboard(data);

                    // 切换到仪表板
                    document.querySelector('[data-section="dashboard"]').click();

                    showNotification('文件上传并分析成功！', 'success');
                } else {
                    showNotification('文件分析失败：' + data.error_message, 'error');
                }

                // 隐藏进度条
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                    progressBar.style.width = '0%';
                }, 2000);
            })
            .catch(error => {
                console.error('上传失败:', error);
                showNotification('文件上传失败', 'error');
                progressContainer.style.display = 'none';
            });
        }

        function loadDashboardData() {
            // 加载示例数据或从API获取数据
            fetch('/health')
            .then(response => response.json())
            .then(data => {
                console.log('系统状态:', data);
                // 可以根据健康检查结果更新UI
            })
            .catch(error => {
                console.error('获取系统状态失败:', error);
            });

            // 创建示例图表
            createSampleCharts();
        }

        function createSampleCharts() {
            // 示例流量时间线
            const timelineData = [{
                x: ['2024-01-01 10:00', '2024-01-01 10:01', '2024-01-01 10:02', '2024-01-01 10:03', '2024-01-01 10:04'],
                y: [100, 150, 120, 180, 160],
                type: 'scatter',
                mode: 'lines+markers',
                name: '包数量',
                line: {color: '#667eea'}
            }];

            Plotly.newPlot('traffic-timeline', timelineData, {
                title: '流量时间线',
                xaxis: {title: '时间'},
                yaxis: {title: '包数量'},
                responsive: true
            });

            // 示例协议分布
            const protocolData = [{
                values: [60, 25, 10, 5],
                labels: ['TCP', 'UDP', 'ICMP', 'Other'],
                type: 'pie',
                hole: 0.3,
                marker: {
                    colors: ['#667eea', '#764ba2', '#f093fb', '#f5576c']
                }
            }];

            Plotly.newPlot('protocol-distribution', protocolData, {
                title: '协议分布',
                responsive: true
            });

            // 示例网络拓扑（简化版）
            const topologyData = [{
                x: [1, 2, 3, 4, 5],
                y: [1, 2, 1, 3, 2],
                mode: 'markers+text',
                type: 'scatter',
                text: ['Router', 'Server1', 'Client1', 'Server2', 'Client2'],
                textposition: 'top center',
                marker: {
                    size: [30, 20, 15, 20, 15],
                    color: ['#667eea', '#764ba2', '#f093fb', '#764ba2', '#f093fb']
                }
            }];

            Plotly.newPlot('network-topology', topologyData, {
                title: '网络拓扑',
                xaxis: {showgrid: false, zeroline: false, showticklabels: false},
                yaxis: {showgrid: false, zeroline: false, showticklabels: false},
                responsive: true
            });
        }

        function updateDashboard(data) {
            if (!data || !data.results) return;

            const results = data.results;

            // 更新统计卡片
            if (results.protocol_stats) {
                document.getElementById('total-packets').textContent =
                    results.protocol_stats.total_packets || 0;
            }

            if (results.traffic_stats) {
                document.getElementById('packet-rate').textContent =
                    (results.traffic_stats.packets_per_second || 0).toFixed(1);
                document.getElementById('bandwidth').textContent =
                    ((results.traffic_stats.bits_per_second || 0) / 1000000).toFixed(2);
            }

            if (results.anomalies) {
                document.getElementById('anomalies').textContent =
                    results.anomalies.length || 0;
            }

            // 更新图表
            if (data.visualizations) {
                // 这里可以加载实际的可视化数据
                console.log('可视化数据:', data.visualizations);
            }
        }

        function refreshDashboard() {
            showNotification('正在刷新仪表板...', 'info');
            loadDashboardData();
        }

        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN');
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show`;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '9999';
            notification.style.minWidth = '300px';

            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // 自动移除通知
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }
    </script>
</body>
</html>
