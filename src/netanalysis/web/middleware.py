#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web中间件模块
Web Middleware Module

提供FastAPI中间件功能，包括请求日志、错误处理、
性能监控、安全防护等中间件。
"""

import logging
import time
from typing import Callable, Dict, Any
from datetime import datetime
import json

try:
    from fastapi import FastAPI, Request, Response
    from fastapi.middleware.base import BaseHTTPMiddleware
    from fastapi.responses import JSONResponse
    import starlette.status as status
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    logging.warning("FastAPI库未安装，中间件功能将受限")


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    请求日志中间件
    
    记录所有HTTP请求的详细信息，包括请求时间、
    响应时间、状态码、用户代理等。
    """
    
    def __init__(self, app, logger_name: str = "request_logger"):
        super().__init__(app)
        self.logger = logging.getLogger(logger_name)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求并记录日志
        
        Args:
            request: HTTP请求对象
            call_next: 下一个中间件或路由处理器
            
        Returns:
            Response: HTTP响应对象
        """
        # 记录请求开始时间
        start_time = time.time()
        
        # 提取请求信息
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        method = request.method
        url = str(request.url)
        
        # 记录请求开始
        self.logger.info(f"请求开始: {method} {url} - 客户端IP: {client_ip}")
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录请求完成
            self.logger.info(
                f"请求完成: {method} {url} - "
                f"状态码: {response.status_code} - "
                f"处理时间: {process_time:.3f}秒 - "
                f"客户端IP: {client_ip} - "
                f"用户代理: {user_agent}"
            )
            
            # 添加响应头
            response.headers["X-Process-Time"] = str(process_time)
            response.headers["X-Request-ID"] = f"req_{int(start_time * 1000)}"
            
            return response
            
        except Exception as e:
            # 记录错误
            process_time = time.time() - start_time
            self.logger.error(
                f"请求异常: {method} {url} - "
                f"错误: {str(e)} - "
                f"处理时间: {process_time:.3f}秒 - "
                f"客户端IP: {client_ip}"
            )
            
            # 返回错误响应
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "error": "内部服务器错误",
                    "message": "请求处理过程中发生异常",
                    "timestamp": datetime.now().isoformat()
                }
            )


class SecurityMiddleware(BaseHTTPMiddleware):
    """
    安全中间件
    
    提供基础的安全防护功能，包括请求频率限制、
    恶意请求检测、安全头设置等。
    """
    
    def __init__(self, app, config: Dict[str, Any] = None):
        super().__init__(app)
        self.config = config or {}
        self.logger = logging.getLogger("security_middleware")
        
        # 配置参数
        self.rate_limit = self.config.get('rate_limit', 100)  # 每分钟请求数限制
        self.max_file_size = self.config.get('max_file_size', 100 * 1024 * 1024)  # 100MB
        self.blocked_ips = set(self.config.get('blocked_ips', []))
        
        # 请求计数器（简单实现，生产环境应使用Redis等）
        self.request_counts = {}
        self.last_reset = time.time()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        安全检查和处理
        
        Args:
            request: HTTP请求对象
            call_next: 下一个中间件或路由处理器
            
        Returns:
            Response: HTTP响应对象
        """
        client_ip = request.client.host if request.client else "unknown"
        
        # 检查IP黑名单
        if client_ip in self.blocked_ips:
            self.logger.warning(f"阻止黑名单IP访问: {client_ip}")
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content={"error": "访问被拒绝", "message": "您的IP地址已被阻止"}
            )
        
        # 频率限制检查
        if not self._check_rate_limit(client_ip):
            self.logger.warning(f"IP {client_ip} 超过频率限制")
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={"error": "请求过于频繁", "message": "请稍后再试"}
            )
        
        # 检查文件大小（对于文件上传）
        if request.method == "POST" and "multipart/form-data" in request.headers.get("content-type", ""):
            content_length = request.headers.get("content-length")
            if content_length and int(content_length) > self.max_file_size:
                self.logger.warning(f"文件大小超限: {content_length} bytes")
                return JSONResponse(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    content={"error": "文件过大", "message": f"文件大小不能超过 {self.max_file_size // 1024 // 1024} MB"}
                )
        
        # 处理请求
        response = await call_next(request)
        
        # 添加安全响应头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        return response
    
    def _check_rate_limit(self, client_ip: str) -> bool:
        """
        检查请求频率限制
        
        Args:
            client_ip: 客户端IP地址
            
        Returns:
            bool: 是否允许请求
        """
        current_time = time.time()
        
        # 每分钟重置计数器
        if current_time - self.last_reset > 60:
            self.request_counts.clear()
            self.last_reset = current_time
        
        # 增加请求计数
        self.request_counts[client_ip] = self.request_counts.get(client_ip, 0) + 1
        
        # 检查是否超过限制
        return self.request_counts[client_ip] <= self.rate_limit


class PerformanceMiddleware(BaseHTTPMiddleware):
    """
    性能监控中间件
    
    监控API性能指标，包括响应时间、内存使用、
    并发请求数等性能数据。
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.logger = logging.getLogger("performance_middleware")
        self.active_requests = 0
        self.total_requests = 0
        self.total_response_time = 0.0
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        性能监控和处理
        
        Args:
            request: HTTP请求对象
            call_next: 下一个中间件或路由处理器
            
        Returns:
            Response: HTTP响应对象
        """
        # 增加活跃请求计数
        self.active_requests += 1
        self.total_requests += 1
        start_time = time.time()
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算性能指标
            response_time = time.time() - start_time
            self.total_response_time += response_time
            avg_response_time = self.total_response_time / self.total_requests
            
            # 记录性能日志
            if response_time > 5.0:  # 响应时间超过5秒记录警告
                self.logger.warning(
                    f"慢请求检测: {request.method} {request.url.path} - "
                    f"响应时间: {response_time:.3f}秒"
                )
            
            # 添加性能响应头
            response.headers["X-Response-Time"] = f"{response_time:.3f}"
            response.headers["X-Active-Requests"] = str(self.active_requests)
            response.headers["X-Total-Requests"] = str(self.total_requests)
            response.headers["X-Avg-Response-Time"] = f"{avg_response_time:.3f}"
            
            return response
            
        finally:
            # 减少活跃请求计数
            self.active_requests -= 1


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """
    错误处理中间件
    
    统一处理应用程序异常，提供友好的错误响应
    和详细的错误日志记录。
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.logger = logging.getLogger("error_middleware")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        错误处理和响应
        
        Args:
            request: HTTP请求对象
            call_next: 下一个中间件或路由处理器
            
        Returns:
            Response: HTTP响应对象
        """
        try:
            return await call_next(request)
            
        except ValueError as e:
            # 参数错误
            self.logger.warning(f"参数错误: {str(e)} - 请求: {request.method} {request.url}")
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={
                    "error": "参数错误",
                    "message": str(e),
                    "timestamp": datetime.now().isoformat()
                }
            )
            
        except FileNotFoundError as e:
            # 文件未找到
            self.logger.warning(f"文件未找到: {str(e)} - 请求: {request.method} {request.url}")
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "error": "文件未找到",
                    "message": "请求的资源不存在",
                    "timestamp": datetime.now().isoformat()
                }
            )
            
        except PermissionError as e:
            # 权限错误
            self.logger.warning(f"权限错误: {str(e)} - 请求: {request.method} {request.url}")
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content={
                    "error": "权限不足",
                    "message": "您没有执行此操作的权限",
                    "timestamp": datetime.now().isoformat()
                }
            )
            
        except Exception as e:
            # 其他未知错误
            self.logger.error(f"未知错误: {str(e)} - 请求: {request.method} {request.url}", exc_info=True)
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "error": "内部服务器错误",
                    "message": "服务器处理请求时发生异常",
                    "timestamp": datetime.now().isoformat()
                }
            )


def setup_middleware(app: 'FastAPI', config: Dict[str, Any] = None) -> None:
    """
    设置所有中间件
    
    Args:
        app: FastAPI应用实例
        config: 中间件配置
    """
    if not FASTAPI_AVAILABLE:
        return
    
    middleware_config = config or {}
    
    # 添加中间件（注意顺序很重要）
    app.add_middleware(ErrorHandlingMiddleware)
    app.add_middleware(PerformanceMiddleware)
    app.add_middleware(SecurityMiddleware, config=middleware_config.get('security', {}))
    app.add_middleware(RequestLoggingMiddleware)
    
    logging.info("所有中间件设置完成")
