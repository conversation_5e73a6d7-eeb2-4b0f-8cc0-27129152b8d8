#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式图表模块
Interactive Charts Module

添加图表交互功能，如缩放、过滤、详情查看等，
提供丰富的用户交互体验和数据探索功能。
"""

import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
import json

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.offline as pyo
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    logging.warning("Plotly库未安装，交互式图表功能将受限")

from ..core.models import Packet, ProtocolStats, TrafficStats


class InteractiveChartManager:
    """
    交互式图表管理器
    
    提供图表交互功能的统一管理，包括缩放、过滤、
    详情查看、数据导出等交互式功能。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化交互式图表管理器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.InteractiveChartManager")
        
        # 配置参数
        self.enable_zoom = self.config.get('enable_zoom', True)
        self.enable_pan = self.config.get('enable_pan', True)
        self.enable_select = self.config.get('enable_select', True)
        self.enable_hover = self.config.get('enable_hover', True)
        self.enable_crossfilter = self.config.get('enable_crossfilter', False)
        
        if not PLOTLY_AVAILABLE:
            self.logger.error("Plotly库未安装，无法创建交互式图表")
        else:
            self.logger.info("交互式图表管理器初始化成功")
    
    def add_interactive_features(self, fig: go.Figure, 
                               chart_type: str = "general") -> go.Figure:
        """
        为图表添加交互式功能
        
        Args:
            fig: Plotly图表对象
            chart_type: 图表类型（time_series, bar, pie, network等）
            
        Returns:
            go.Figure: 增强后的交互式图表
        """
        if not PLOTLY_AVAILABLE or not fig:
            return fig
        
        try:
            self.logger.info(f"为{chart_type}图表添加交互式功能")
            
            # 基础交互配置
            fig.update_layout(
                # 启用缩放和平移
                xaxis=dict(
                    rangeslider=dict(visible=False) if chart_type == "time_series" else None,
                    type='date' if chart_type == "time_series" else None
                ),
                # 悬停模式
                hovermode='closest' if chart_type != "time_series" else 'x unified',
                # 选择工具
                dragmode='zoom' if self.enable_zoom else 'pan',
                # 工具栏配置
                modebar=dict(
                    bgcolor='rgba(255,255,255,0.8)',
                    color='rgba(0,0,0,0.5)',
                    activecolor='rgba(0,0,0,0.8)'
                )
            )
            
            # 根据图表类型添加特定功能
            if chart_type == "time_series":
                fig = self._add_time_series_features(fig)
            elif chart_type == "bar":
                fig = self._add_bar_chart_features(fig)
            elif chart_type == "pie":
                fig = self._add_pie_chart_features(fig)
            elif chart_type == "network":
                fig = self._add_network_features(fig)
            
            # 添加通用交互功能
            fig = self._add_general_features(fig)
            
            self.logger.info("交互式功能添加完成")
            return fig
            
        except Exception as e:
            self.logger.error(f"添加交互式功能失败: {str(e)}")
            return fig
    
    def _add_time_series_features(self, fig: go.Figure) -> go.Figure:
        """
        为时序图添加特定的交互功能
        
        Args:
            fig: 时序图对象
            
        Returns:
            go.Figure: 增强后的时序图
        """
        # 添加范围选择器
        fig.update_layout(
            xaxis=dict(
                rangeselector=dict(
                    buttons=list([
                        dict(count=1, label="1分钟", step="minute", stepmode="backward"),
                        dict(count=5, label="5分钟", step="minute", stepmode="backward"),
                        dict(count=1, label="1小时", step="hour", stepmode="backward"),
                        dict(step="all", label="全部")
                    ])
                ),
                rangeslider=dict(visible=True),
                type='date'
            )
        )
        
        # 添加十字线
        fig.update_layout(
            hovermode='x unified',
            hoverlabel=dict(
                bgcolor="white",
                font_size=12,
                font_family="Arial"
            )
        )
        
        # 添加缩放重置按钮
        fig.update_layout(
            updatemenus=[
                dict(
                    type="buttons",
                    direction="left",
                    buttons=list([
                        dict(
                            args=[{"xaxis.autorange": True, "yaxis.autorange": True}],
                            label="重置缩放",
                            method="relayout"
                        )
                    ]),
                    pad={"r": 10, "t": 10},
                    showactive=True,
                    x=0.01,
                    xanchor="left",
                    y=1.02,
                    yanchor="top"
                ),
            ]
        )
        
        return fig
    
    def _add_bar_chart_features(self, fig: go.Figure) -> go.Figure:
        """
        为柱状图添加特定的交互功能
        
        Args:
            fig: 柱状图对象
            
        Returns:
            go.Figure: 增强后的柱状图
        """
        # 添加排序功能
        fig.update_layout(
            updatemenus=[
                dict(
                    type="dropdown",
                    direction="down",
                    buttons=list([
                        dict(
                            args=[{"transforms[0].operation": "sort", 
                                  "transforms[0].target": "y", 
                                  "transforms[0].order": "descending"}],
                            label="降序排列",
                            method="restyle"
                        ),
                        dict(
                            args=[{"transforms[0].operation": "sort", 
                                  "transforms[0].target": "y", 
                                  "transforms[0].order": "ascending"}],
                            label="升序排列",
                            method="restyle"
                        ),
                        dict(
                            args=[{"transforms[0].operation": None}],
                            label="原始顺序",
                            method="restyle"
                        )
                    ]),
                    pad={"r": 10, "t": 10},
                    showactive=True,
                    x=0.01,
                    xanchor="left",
                    y=1.02,
                    yanchor="top"
                )
            ]
        )
        
        # 添加点击选择功能
        fig.update_traces(
            selected=dict(marker=dict(color="red", opacity=0.8)),
            unselected=dict(marker=dict(opacity=0.4))
        )
        
        return fig
    
    def _add_pie_chart_features(self, fig: go.Figure) -> go.Figure:
        """
        为饼图添加特定的交互功能
        
        Args:
            fig: 饼图对象
            
        Returns:
            go.Figure: 增强后的饼图
        """
        # 添加扇形拉出功能
        fig.update_traces(
            pull=[0.1 if i == 0 else 0 for i in range(len(fig.data[0].labels))],
            hoverinfo="label+percent+value",
            textinfo="label+percent",
            textfont_size=12
        )
        
        # 添加显示/隐藏标签的切换
        fig.update_layout(
            updatemenus=[
                dict(
                    type="buttons",
                    direction="left",
                    buttons=list([
                        dict(
                            args=[{"textinfo": "label+percent"}],
                            label="显示标签",
                            method="restyle"
                        ),
                        dict(
                            args=[{"textinfo": "percent"}],
                            label="仅显示百分比",
                            method="restyle"
                        ),
                        dict(
                            args=[{"textinfo": "none"}],
                            label="隐藏标签",
                            method="restyle"
                        )
                    ]),
                    pad={"r": 10, "t": 10},
                    showactive=True,
                    x=0.01,
                    xanchor="left",
                    y=1.02,
                    yanchor="top"
                )
            ]
        )
        
        return fig
    
    def _add_network_features(self, fig: go.Figure) -> go.Figure:
        """
        为网络图添加特定的交互功能
        
        Args:
            fig: 网络图对象
            
        Returns:
            go.Figure: 增强后的网络图
        """
        # 禁用坐标轴
        fig.update_layout(
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            showlegend=False,
            hovermode='closest'
        )
        
        # 添加节点选择功能
        if fig.data and len(fig.data) > 1:  # 确保有节点数据
            fig.update_traces(
                selector=dict(mode="markers"),
                selected=dict(marker=dict(size=20, line=dict(width=3, color="red"))),
                unselected=dict(marker=dict(opacity=0.5))
            )
        
        return fig
    
    def _add_general_features(self, fig: go.Figure) -> go.Figure:
        """
        添加通用的交互功能
        
        Args:
            fig: 图表对象
            
        Returns:
            go.Figure: 增强后的图表
        """
        # 配置工具栏
        config = {
            'displayModeBar': True,
            'displaylogo': False,
            'modeBarButtonsToAdd': [
                'drawline',
                'drawopenpath',
                'drawclosedpath',
                'drawcircle',
                'drawrect',
                'eraseshape'
            ],
            'modeBarButtonsToRemove': ['lasso2d'],
            'toImageButtonOptions': {
                'format': 'png',
                'filename': f'chart_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
                'height': 600,
                'width': 1000,
                'scale': 2
            }
        }
        
        # 存储配置到图表
        fig._config = config
        
        # 添加注释功能
        fig.update_layout(
            annotations=[
                dict(
                    text="双击重置缩放 | 拖拽选择区域 | 悬停查看详情",
                    showarrow=False,
                    xref="paper", yref="paper",
                    x=0.5, y=-0.1,
                    xanchor='center', yanchor='bottom',
                    font=dict(color='gray', size=10)
                )
            ]
        )
        
        return fig
    
    def create_dashboard(self, charts: List[go.Figure], 
                        layout: str = "grid") -> go.Figure:
        """
        创建交互式仪表板
        
        Args:
            charts: 图表列表
            layout: 布局方式（grid, tabs, accordion）
            
        Returns:
            go.Figure: 仪表板图表
        """
        if not PLOTLY_AVAILABLE or not charts:
            return None
        
        try:
            self.logger.info(f"创建交互式仪表板，布局: {layout}")
            
            if layout == "grid":
                return self._create_grid_dashboard(charts)
            elif layout == "tabs":
                return self._create_tabbed_dashboard(charts)
            else:
                return self._create_grid_dashboard(charts)
            
        except Exception as e:
            self.logger.error(f"创建仪表板失败: {str(e)}")
            return None
    
    def _create_grid_dashboard(self, charts: List[go.Figure]) -> go.Figure:
        """
        创建网格布局仪表板
        
        Args:
            charts: 图表列表
            
        Returns:
            go.Figure: 网格仪表板
        """
        # 计算网格布局
        n_charts = len(charts)
        if n_charts <= 2:
            rows, cols = 1, n_charts
        elif n_charts <= 4:
            rows, cols = 2, 2
        elif n_charts <= 6:
            rows, cols = 2, 3
        else:
            rows, cols = 3, 3
        
        # 创建子图
        fig = make_subplots(
            rows=rows, cols=cols,
            subplot_titles=[f"图表 {i+1}" for i in range(min(n_charts, rows*cols))],
            vertical_spacing=0.08,
            horizontal_spacing=0.05
        )
        
        # 添加图表到子图
        for i, chart in enumerate(charts[:rows*cols]):
            row = i // cols + 1
            col = i % cols + 1
            
            # 复制图表数据
            for trace in chart.data:
                fig.add_trace(trace, row=row, col=col)
        
        # 更新布局
        fig.update_layout(
            title="网络分析仪表板",
            height=300 * rows,
            width=1200,
            showlegend=False
        )
        
        return fig
    
    def _create_tabbed_dashboard(self, charts: List[go.Figure]) -> go.Figure:
        """
        创建标签页仪表板
        
        Args:
            charts: 图表列表
            
        Returns:
            go.Figure: 标签页仪表板
        """
        # 创建第一个图表作为基础
        fig = charts[0] if charts else go.Figure()
        
        # 添加标签页切换功能
        buttons = []
        for i, chart in enumerate(charts):
            buttons.append(
                dict(
                    args=[{"visible": [j == i for j in range(len(charts))]}],
                    label=f"图表 {i+1}",
                    method="restyle"
                )
            )
        
        fig.update_layout(
            updatemenus=[
                dict(
                    type="buttons",
                    direction="left",
                    buttons=buttons,
                    pad={"r": 10, "t": 10},
                    showactive=True,
                    x=0.01,
                    xanchor="left",
                    y=1.02,
                    yanchor="top"
                )
            ]
        )
        
        return fig
    
    def add_data_export_feature(self, fig: go.Figure, 
                              data: Dict[str, Any]) -> go.Figure:
        """
        添加数据导出功能
        
        Args:
            fig: 图表对象
            data: 原始数据
            
        Returns:
            go.Figure: 增强后的图表
        """
        if not PLOTLY_AVAILABLE:
            return fig
        
        try:
            # 添加导出按钮
            export_buttons = [
                dict(
                    args=[{"visible": True}],
                    label="导出PNG",
                    method="restyle"
                ),
                dict(
                    args=[{"visible": True}],
                    label="导出HTML",
                    method="restyle"
                ),
                dict(
                    args=[{"visible": True}],
                    label="导出数据",
                    method="restyle"
                )
            ]
            
            # 更新布局添加导出菜单
            current_menus = fig.layout.updatemenus or []
            fig.update_layout(
                updatemenus=current_menus + [
                    dict(
                        type="dropdown",
                        direction="down",
                        buttons=export_buttons,
                        pad={"r": 10, "t": 10},
                        showactive=True,
                        x=1.0,
                        xanchor="right",
                        y=1.02,
                        yanchor="top"
                    )
                ]
            )
            
            return fig
            
        except Exception as e:
            self.logger.error(f"添加导出功能失败: {str(e)}")
            return fig
    
    def save_interactive_chart(self, fig: go.Figure, filename: str, 
                             include_plotlyjs: str = "cdn") -> bool:
        """
        保存交互式图表
        
        Args:
            fig: 图表对象
            filename: 文件名
            include_plotlyjs: Plotly.js包含方式
            
        Returns:
            bool: 是否保存成功
        """
        if not PLOTLY_AVAILABLE or not fig:
            return False
        
        try:
            # 获取配置
            config = getattr(fig, '_config', {})
            
            # 保存为HTML文件
            fig.write_html(
                filename,
                include_plotlyjs=include_plotlyjs,
                config=config,
                div_id="chart_div"
            )
            
            self.logger.info(f"交互式图表已保存到: {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存交互式图表失败: {str(e)}")
            return False
