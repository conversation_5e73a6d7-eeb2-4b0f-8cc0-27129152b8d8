#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化引擎
Visualization Engine

统一管理所有可视化功能，提供一站式的网络数据可视化服务。
集成时序图、协议图表、网络拓扑图和交互式图表功能。
"""

import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import os

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.offline as pyo
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    logging.warning("Plotly库未安装，可视化功能将受限")

from .time_series import TimeSeriesVisualizer
from .protocol_charts import ProtocolChartVisualizer
from .network_topology import NetworkTopologyVisualizer
from .interactive_charts import InteractiveChartManager
from ..core.models import Packet, ProtocolStats, TrafficStats


class VisualizationEngine:
    """
    可视化引擎
    
    统一管理所有可视化功能，提供完整的网络数据可视化解决方案。
    支持时序图、协议图表、网络拓扑图和交互式图表的创建和管理。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化可视化引擎
        
        Args:
            config: 配置参数，包括：
                - output_dir: 输出目录（默认./visualizations）
                - theme: 主题样式（默认plotly）
                - width: 默认图表宽度（默认1200）
                - height: 默认图表高度（默认600）
                - enable_interactive: 启用交互功能（默认True）
                - auto_save: 自动保存图表（默认False）
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.VisualizationEngine")
        
        # 配置参数
        self.output_dir = self.config.get('output_dir', './visualizations')
        self.theme = self.config.get('theme', 'plotly')
        self.width = self.config.get('width', 1200)
        self.height = self.config.get('height', 600)
        self.enable_interactive = self.config.get('enable_interactive', True)
        self.auto_save = self.config.get('auto_save', False)
        
        # 创建输出目录
        if self.auto_save:
            os.makedirs(self.output_dir, exist_ok=True)
        
        # 初始化各个可视化器
        viz_config = {
            'theme': self.theme,
            'width': self.width,
            'height': self.height
        }
        
        self.time_series_viz = TimeSeriesVisualizer(viz_config)
        self.protocol_viz = ProtocolChartVisualizer(viz_config)
        self.topology_viz = NetworkTopologyVisualizer(viz_config)
        
        if self.enable_interactive:
            self.interactive_manager = InteractiveChartManager(self.config)
        
        if not PLOTLY_AVAILABLE:
            self.logger.error("Plotly库未安装，可视化功能将受限")
        else:
            self.logger.info("可视化引擎初始化成功")
    
    def create_comprehensive_dashboard(self, packets: List[Packet], 
                                     protocol_stats: ProtocolStats,
                                     traffic_stats: TrafficStats) -> Optional[go.Figure]:
        """
        创建综合仪表板
        
        Args:
            packets: 数据包列表
            protocol_stats: 协议统计数据
            traffic_stats: 流量统计数据
            
        Returns:
            Optional[go.Figure]: 综合仪表板图表
        """
        if not PLOTLY_AVAILABLE:
            self.logger.error("Plotly不可用，无法创建综合仪表板")
            return None
        
        try:
            self.logger.info("创建综合网络分析仪表板")
            
            # 创建子图布局
            fig = make_subplots(
                rows=3, cols=2,
                subplot_titles=(
                    '流量时间线', '协议分布',
                    '带宽使用', 'Top端口统计',
                    '连接统计', '网络概览'
                ),
                specs=[
                    [{"colspan": 1}, {"type": "pie"}],
                    [{"secondary_y": True}, {"type": "bar"}],
                    [{"colspan": 2}, None]
                ],
                vertical_spacing=0.08,
                horizontal_spacing=0.1
            )
            
            # 1. 流量时间线（简化版）
            traffic_data = self._prepare_simple_traffic_data(packets)
            if traffic_data:
                fig.add_trace(
                    go.Scatter(
                        x=traffic_data['timestamps'],
                        y=traffic_data['packet_counts'],
                        mode='lines+markers',
                        name='包数量',
                        line=dict(color='#1f77b4', width=2)
                    ),
                    row=1, col=1
                )
            
            # 2. 协议分布饼图
            if protocol_stats.protocol_counts:
                protocols = list(protocol_stats.protocol_counts.keys())
                counts = list(protocol_stats.protocol_counts.values())
                
                fig.add_trace(
                    go.Pie(
                        labels=[p.upper() for p in protocols],
                        values=counts,
                        name="协议分布",
                        hole=0.3,
                        textinfo='label+percent'
                    ),
                    row=1, col=2
                )
            
            # 3. 带宽使用
            bandwidth_data = self._prepare_bandwidth_data(packets)
            if bandwidth_data:
                fig.add_trace(
                    go.Scatter(
                        x=bandwidth_data['timestamps'],
                        y=bandwidth_data['bandwidth_mbps'],
                        mode='lines+markers',
                        name='带宽 (Mbps)',
                        line=dict(color='#ff7f0e', width=2)
                    ),
                    row=2, col=1
                )
            
            # 4. Top端口统计
            port_data = self._prepare_port_data(packets)
            if port_data:
                fig.add_trace(
                    go.Bar(
                        x=port_data['ports'],
                        y=port_data['counts'],
                        name='端口使用',
                        marker_color='lightblue'
                    ),
                    row=2, col=2
                )
            
            # 5. 连接统计概览
            connection_summary = self._create_connection_summary(traffic_stats)
            fig.add_trace(
                go.Indicator(
                    mode="number+gauge+delta",
                    value=traffic_stats.total_connections,
                    domain={'x': [0, 1], 'y': [0, 1]},
                    title={"text": "总连接数"},
                    gauge={'axis': {'range': [None, traffic_stats.total_connections * 1.5]},
                           'bar': {'color': "darkblue"},
                           'steps': [{'range': [0, traffic_stats.total_connections * 0.5], 'color': "lightgray"},
                                   {'range': [traffic_stats.total_connections * 0.5, traffic_stats.total_connections], 'color': "gray"}],
                           'threshold': {'line': {'color': "red", 'width': 4},
                                       'thickness': 0.75, 'value': traffic_stats.total_connections * 0.9}}
                ),
                row=3, col=1
            )
            
            # 更新布局
            fig.update_layout(
                title=f'网络流量综合分析仪表板 - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                width=self.width * 1.5,
                height=self.height * 1.8,
                showlegend=True,
                template=self.theme
            )
            
            # 添加交互功能
            if self.enable_interactive:
                fig = self.interactive_manager.add_interactive_features(fig, "dashboard")
            
            # 自动保存
            if self.auto_save:
                self._save_figure(fig, "comprehensive_dashboard.html")
            
            self.logger.info("综合仪表板创建成功")
            return fig
            
        except Exception as e:
            error_msg = f"创建综合仪表板失败: {str(e)}"
            self.logger.error(error_msg)
            return None
    
    def create_time_series_charts(self, packets: List[Packet], 
                                time_window: str = "1min") -> Dict[str, Optional[go.Figure]]:
        """
        创建时序图表集合
        
        Args:
            packets: 数据包列表
            time_window: 时间窗口
            
        Returns:
            Dict[str, Optional[go.Figure]]: 时序图表字典
        """
        charts = {}
        
        # 流量时间线
        charts['traffic_timeline'] = self.time_series_viz.create_traffic_timeline(packets, time_window)
        
        # 协议时间线
        charts['protocol_timeline'] = self.time_series_viz.create_protocol_timeline(packets, time_window)
        
        # 带宽时间线
        charts['bandwidth_timeline'] = self.time_series_viz.create_bandwidth_timeline(packets, time_window)
        
        # 添加交互功能
        if self.enable_interactive:
            for name, chart in charts.items():
                if chart:
                    charts[name] = self.interactive_manager.add_interactive_features(chart, "time_series")
        
        # 自动保存
        if self.auto_save:
            for name, chart in charts.items():
                if chart:
                    self._save_figure(chart, f"{name}_{time_window}.html")
        
        return charts
    
    def create_protocol_charts(self, protocol_stats: ProtocolStats) -> Dict[str, Optional[go.Figure]]:
        """
        创建协议图表集合
        
        Args:
            protocol_stats: 协议统计数据
            
        Returns:
            Dict[str, Optional[go.Figure]]: 协议图表字典
        """
        charts = {}
        
        # 协议饼图
        charts['protocol_pie'] = self.protocol_viz.create_protocol_pie_chart(protocol_stats)
        
        # 协议柱状图
        charts['protocol_bar'] = self.protocol_viz.create_protocol_bar_chart(protocol_stats)
        
        # 添加交互功能
        if self.enable_interactive:
            for name, chart in charts.items():
                if chart:
                    chart_type = "pie" if "pie" in name else "bar"
                    charts[name] = self.interactive_manager.add_interactive_features(chart, chart_type)
        
        # 自动保存
        if self.auto_save:
            for name, chart in charts.items():
                if chart:
                    self._save_figure(chart, f"{name}.html")
        
        return charts
    
    def create_network_topology(self, packets: List[Packet], 
                              show_protocols: bool = True) -> Optional[go.Figure]:
        """
        创建网络拓扑图
        
        Args:
            packets: 数据包列表
            show_protocols: 是否显示协议信息
            
        Returns:
            Optional[go.Figure]: 网络拓扑图
        """
        topology_chart = self.topology_viz.create_network_topology(packets, show_protocols)
        
        # 添加交互功能
        if self.enable_interactive and topology_chart:
            topology_chart = self.interactive_manager.add_interactive_features(topology_chart, "network")
        
        # 自动保存
        if self.auto_save and topology_chart:
            self._save_figure(topology_chart, "network_topology.html")
        
        return topology_chart
    
    def _prepare_simple_traffic_data(self, packets: List[Packet]) -> Optional[Dict[str, List]]:
        """准备简化的流量数据"""
        if not packets:
            return None
        
        # 简单的时间分组
        from collections import defaultdict
        time_groups = defaultdict(int)
        
        for packet in packets:
            if packet.timestamp:
                # 按分钟分组
                time_key = packet.timestamp.replace(second=0, microsecond=0)
                time_groups[time_key] += 1
        
        timestamps = sorted(time_groups.keys())
        packet_counts = [time_groups[ts] for ts in timestamps]
        
        return {
            'timestamps': timestamps,
            'packet_counts': packet_counts
        }
    
    def _prepare_bandwidth_data(self, packets: List[Packet]) -> Optional[Dict[str, List]]:
        """准备带宽数据"""
        if not packets:
            return None
        
        from collections import defaultdict
        time_groups = defaultdict(int)
        
        for packet in packets:
            if packet.timestamp:
                time_key = packet.timestamp.replace(second=0, microsecond=0)
                time_groups[time_key] += packet.size
        
        timestamps = sorted(time_groups.keys())
        bandwidth_mbps = [(time_groups[ts] * 8) / (60 * 1000000) for ts in timestamps]  # 转换为Mbps
        
        return {
            'timestamps': timestamps,
            'bandwidth_mbps': bandwidth_mbps
        }
    
    def _prepare_port_data(self, packets: List[Packet]) -> Optional[Dict[str, List]]:
        """准备端口数据"""
        if not packets:
            return None
        
        from collections import Counter
        port_counts = Counter()
        
        for packet in packets:
            if packet.dst_port:
                port_counts[packet.dst_port] += 1
        
        # 取前10个端口
        top_ports = port_counts.most_common(10)
        ports = [str(port) for port, _ in top_ports]
        counts = [count for _, count in top_ports]
        
        return {
            'ports': ports,
            'counts': counts
        }
    
    def _create_connection_summary(self, traffic_stats: TrafficStats) -> Dict[str, Any]:
        """创建连接摘要"""
        return {
            'total_connections': traffic_stats.total_connections,
            'active_connections': traffic_stats.active_connections,
            'connection_rate': traffic_stats.total_connections / max(1, traffic_stats.duration_seconds) if hasattr(traffic_stats, 'duration_seconds') else 0
        }
    
    def _save_figure(self, fig: go.Figure, filename: str) -> bool:
        """保存图表到文件"""
        try:
            filepath = os.path.join(self.output_dir, filename)
            fig.write_html(filepath)
            self.logger.info(f"图表已保存到: {filepath}")
            return True
        except Exception as e:
            self.logger.error(f"保存图表失败: {str(e)}")
            return False
    
    def export_all_charts(self, packets: List[Packet], 
                         protocol_stats: ProtocolStats,
                         traffic_stats: TrafficStats,
                         format: str = "html") -> Dict[str, bool]:
        """
        导出所有图表
        
        Args:
            packets: 数据包列表
            protocol_stats: 协议统计数据
            traffic_stats: 流量统计数据
            format: 导出格式（html, png, pdf）
            
        Returns:
            Dict[str, bool]: 导出结果
        """
        results = {}
        
        try:
            # 创建所有图表
            dashboard = self.create_comprehensive_dashboard(packets, protocol_stats, traffic_stats)
            time_charts = self.create_time_series_charts(packets)
            protocol_charts = self.create_protocol_charts(protocol_stats)
            topology = self.create_network_topology(packets)
            
            # 导出图表
            charts_to_export = {
                'dashboard': dashboard,
                'topology': topology,
                **time_charts,
                **protocol_charts
            }
            
            for name, chart in charts_to_export.items():
                if chart:
                    filename = f"{name}.{format}"
                    if format == "html":
                        results[name] = self._save_figure(chart, filename)
                    else:
                        # 其他格式需要额外处理
                        try:
                            filepath = os.path.join(self.output_dir, filename)
                            chart.write_image(filepath, format=format)
                            results[name] = True
                        except Exception as e:
                            self.logger.error(f"导出{name}图表失败: {str(e)}")
                            results[name] = False
                else:
                    results[name] = False
            
            self.logger.info(f"图表导出完成，成功: {sum(results.values())}/{len(results)}")
            return results
            
        except Exception as e:
            self.logger.error(f"导出图表失败: {str(e)}")
            return {}
    
    def get_visualization_summary(self) -> Dict[str, Any]:
        """
        获取可视化引擎摘要信息
        
        Returns:
            Dict[str, Any]: 摘要信息
        """
        return {
            'engine_status': 'available' if PLOTLY_AVAILABLE else 'limited',
            'output_directory': self.output_dir,
            'theme': self.theme,
            'interactive_enabled': self.enable_interactive,
            'auto_save_enabled': self.auto_save,
            'supported_charts': [
                'comprehensive_dashboard',
                'traffic_timeline',
                'protocol_timeline',
                'bandwidth_timeline',
                'protocol_pie_chart',
                'protocol_bar_chart',
                'network_topology'
            ],
            'supported_formats': ['html', 'png', 'pdf', 'svg'] if PLOTLY_AVAILABLE else []
        }
