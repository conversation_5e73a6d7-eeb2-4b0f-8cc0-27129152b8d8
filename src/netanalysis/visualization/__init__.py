#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化模块
Visualization Module

实现交互式图表和可视化功能，包括时序图、协议分布图、
网络拓扑图等多种可视化组件。
"""

from .time_series import TimeSeriesVisualizer
from .protocol_charts import ProtocolChartVisualizer
from .network_topology import NetworkTopologyVisualizer
from .interactive_charts import InteractiveChartManager

# 检查依赖库可用性
try:
    import plotly.graph_objects as go
    import plotly.express as px
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

try:
    import networkx as nx
    NETWORKX_AVAILABLE = True
except ImportError:
    NETWORKX_AVAILABLE = False

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

__all__ = [
    # 可视化器类
    'TimeSeriesVisualizer',
    'ProtocolChartVisualizer',
    'NetworkTopologyVisualizer',
    'InteractiveChartManager',

    # 可用性标志
    'PLOTLY_AVAILABLE',
    'NETWORKX_AVAILABLE',
    'PANDAS_AVAILABLE'
]