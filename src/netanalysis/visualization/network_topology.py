#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络拓扑图模块
Network Topology Visualization Module

实现网络连接关系的拓扑图可视化，支持节点和边的交互式展示，
包括IP地址节点、连接关系、流量强度等网络拓扑信息。
"""

import logging
from typing import Dict, List, Optional, Any, Tuple, Set
from collections import defaultdict
import math

try:
    import plotly.graph_objects as go
    import plotly.express as px
    import networkx as nx
    PLOTLY_AVAILABLE = True
    NETWORKX_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    NETWORKX_AVAILABLE = False
    logging.warning("Plotly或NetworkX库未安装，网络拓扑图功能将受限")

from ..core.models import Packet, ProtocolType


class NetworkTopologyVisualizer:
    """
    网络拓扑图可视化器
    
    创建网络连接关系的拓扑图，展示IP地址之间的连接关系、
    流量强度、协议分布等网络拓扑信息。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化网络拓扑图可视化器
        
        Args:
            config: 配置参数，包括：
                - layout: 布局算法（spring, circular, kamada_kawai）
                - node_size_factor: 节点大小因子（默认10）
                - edge_width_factor: 边宽度因子（默认5）
                - min_connections: 最小连接数阈值（默认1）
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.NetworkTopologyVisualizer")
        
        # 配置参数
        self.layout = self.config.get('layout', 'spring')
        self.node_size_factor = self.config.get('node_size_factor', 10)
        self.edge_width_factor = self.config.get('edge_width_factor', 5)
        self.min_connections = self.config.get('min_connections', 1)
        self.width = self.config.get('width', 1200)
        self.height = self.config.get('height', 800)
        
        if not PLOTLY_AVAILABLE or not NETWORKX_AVAILABLE:
            self.logger.error("Plotly或NetworkX库未安装，无法创建网络拓扑图")
        else:
            self.logger.info("网络拓扑图可视化器初始化成功")
    
    def create_network_topology(self, packets: List[Packet], 
                              show_protocols: bool = True) -> Optional[go.Figure]:
        """
        创建网络拓扑图
        
        Args:
            packets: 数据包列表
            show_protocols: 是否显示协议信息
            
        Returns:
            Optional[go.Figure]: Plotly图表对象
        """
        if not PLOTLY_AVAILABLE or not NETWORKX_AVAILABLE:
            self.logger.error("依赖库不可用，无法创建网络拓扑图")
            return None
        
        if not packets:
            self.logger.warning("数据包列表为空")
            return None
        
        try:
            self.logger.info("创建网络拓扑图")
            
            # 构建网络图
            G = self._build_network_graph(packets)
            
            if G.number_of_nodes() == 0:
                self.logger.warning("没有有效的网络节点")
                return None
            
            # 计算布局
            pos = self._calculate_layout(G)
            
            # 创建Plotly图表
            fig = self._create_plotly_network(G, pos, show_protocols)
            
            self.logger.info(f"网络拓扑图创建成功，节点数: {G.number_of_nodes()}, 边数: {G.number_of_edges()}")
            return fig
            
        except Exception as e:
            error_msg = f"创建网络拓扑图失败: {str(e)}"
            self.logger.error(error_msg)
            return None
    
    def create_protocol_topology(self, packets: List[Packet], 
                               protocol: ProtocolType) -> Optional[go.Figure]:
        """
        创建特定协议的网络拓扑图
        
        Args:
            packets: 数据包列表
            protocol: 协议类型
            
        Returns:
            Optional[go.Figure]: Plotly图表对象
        """
        if not PLOTLY_AVAILABLE or not NETWORKX_AVAILABLE:
            self.logger.error("依赖库不可用，无法创建协议拓扑图")
            return None
        
        # 过滤特定协议的数据包
        protocol_packets = [p for p in packets if p.protocol == protocol]
        
        if not protocol_packets:
            self.logger.warning(f"没有找到{protocol.value}协议的数据包")
            return None
        
        try:
            self.logger.info(f"创建{protocol.value}协议拓扑图")
            
            # 构建网络图
            G = self._build_network_graph(protocol_packets)
            
            # 计算布局
            pos = self._calculate_layout(G)
            
            # 创建Plotly图表
            fig = self._create_plotly_network(G, pos, show_protocols=False)
            
            # 更新标题
            fig.update_layout(title=f'{protocol.value.upper()}协议网络拓扑图')
            
            self.logger.info(f"{protocol.value}协议拓扑图创建成功")
            return fig
            
        except Exception as e:
            error_msg = f"创建{protocol.value}协议拓扑图失败: {str(e)}"
            self.logger.error(error_msg)
            return None
    
    def create_traffic_flow_diagram(self, packets: List[Packet]) -> Optional[go.Figure]:
        """
        创建流量流向图
        
        Args:
            packets: 数据包列表
            
        Returns:
            Optional[go.Figure]: Plotly图表对象
        """
        if not PLOTLY_AVAILABLE:
            self.logger.error("Plotly不可用，无法创建流量流向图")
            return None
        
        try:
            self.logger.info("创建流量流向图")
            
            # 分析流量流向
            flow_data = self._analyze_traffic_flows(packets)
            
            if not flow_data:
                self.logger.warning("没有有效的流量流向数据")
                return None
            
            # 创建Sankey图
            fig = self._create_sankey_diagram(flow_data)
            
            self.logger.info("流量流向图创建成功")
            return fig
            
        except Exception as e:
            error_msg = f"创建流量流向图失败: {str(e)}"
            self.logger.error(error_msg)
            return None
    
    def _build_network_graph(self, packets: List[Packet]) -> 'nx.Graph':
        """
        构建网络图
        
        Args:
            packets: 数据包列表
            
        Returns:
            nx.Graph: NetworkX图对象
        """
        G = nx.Graph()
        
        # 统计连接信息
        connections = defaultdict(lambda: {'count': 0, 'bytes': 0, 'protocols': set()})
        node_info = defaultdict(lambda: {'in_degree': 0, 'out_degree': 0, 'total_bytes': 0})
        
        for packet in packets:
            if packet.src_ip and packet.dst_ip:
                # 创建连接键（无方向）
                edge_key = tuple(sorted([packet.src_ip, packet.dst_ip]))
                
                # 更新连接统计
                connections[edge_key]['count'] += 1
                connections[edge_key]['bytes'] += packet.size
                if packet.protocol:
                    connections[edge_key]['protocols'].add(packet.protocol.value)
                
                # 更新节点统计
                node_info[packet.src_ip]['out_degree'] += 1
                node_info[packet.src_ip]['total_bytes'] += packet.size
                node_info[packet.dst_ip]['in_degree'] += 1
        
        # 添加节点
        for ip, info in node_info.items():
            if info['out_degree'] + info['in_degree'] >= self.min_connections:
                G.add_node(ip, 
                          in_degree=info['in_degree'],
                          out_degree=info['out_degree'],
                          total_bytes=info['total_bytes'],
                          total_connections=info['in_degree'] + info['out_degree'])
        
        # 添加边
        for (src, dst), info in connections.items():
            if info['count'] >= self.min_connections and src in G.nodes and dst in G.nodes:
                G.add_edge(src, dst,
                          weight=info['count'],
                          bytes=info['bytes'],
                          protocols=list(info['protocols']))
        
        return G
    
    def _calculate_layout(self, G: 'nx.Graph') -> Dict[str, Tuple[float, float]]:
        """
        计算网络布局
        
        Args:
            G: NetworkX图对象
            
        Returns:
            Dict[str, Tuple[float, float]]: 节点位置字典
        """
        if self.layout == 'circular':
            pos = nx.circular_layout(G)
        elif self.layout == 'kamada_kawai':
            pos = nx.kamada_kawai_layout(G)
        elif self.layout == 'random':
            pos = nx.random_layout(G)
        else:  # spring layout (default)
            pos = nx.spring_layout(G, k=1, iterations=50)
        
        return pos
    
    def _create_plotly_network(self, G: 'nx.Graph', pos: Dict[str, Tuple[float, float]], 
                             show_protocols: bool = True) -> go.Figure:
        """
        创建Plotly网络图
        
        Args:
            G: NetworkX图对象
            pos: 节点位置
            show_protocols: 是否显示协议信息
            
        Returns:
            go.Figure: Plotly图表对象
        """
        # 准备边数据
        edge_x = []
        edge_y = []
        edge_info = []
        
        for edge in G.edges():
            x0, y0 = pos[edge[0]]
            x1, y1 = pos[edge[1]]
            edge_x.extend([x0, x1, None])
            edge_y.extend([y0, y1, None])
            
            # 边信息
            edge_data = G.edges[edge]
            protocols = ', '.join(edge_data.get('protocols', []))
            info = f"连接: {edge[0]} ↔ {edge[1]}<br>"
            info += f"包数量: {edge_data.get('weight', 0):,}<br>"
            info += f"字节数: {edge_data.get('bytes', 0):,}<br>"
            if show_protocols and protocols:
                info += f"协议: {protocols}"
            edge_info.append(info)
        
        # 创建边轨迹
        edge_trace = go.Scatter(
            x=edge_x, y=edge_y,
            line=dict(width=2, color='lightgray'),
            hoverinfo='none',
            mode='lines'
        )
        
        # 准备节点数据
        node_x = []
        node_y = []
        node_text = []
        node_info = []
        node_sizes = []
        node_colors = []
        
        for node in G.nodes():
            x, y = pos[node]
            node_x.append(x)
            node_y.append(y)
            
            # 节点信息
            node_data = G.nodes[node]
            connections = node_data.get('total_connections', 0)
            total_bytes = node_data.get('total_bytes', 0)
            
            # 节点大小（基于连接数）
            size = max(10, connections * self.node_size_factor)
            node_sizes.append(size)
            
            # 节点颜色（基于流量）
            node_colors.append(total_bytes)
            
            # 节点标签
            node_text.append(node)
            
            # 悬停信息
            info = f"IP地址: {node}<br>"
            info += f"连接数: {connections}<br>"
            info += f"入度: {node_data.get('in_degree', 0)}<br>"
            info += f"出度: {node_data.get('out_degree', 0)}<br>"
            info += f"总字节数: {total_bytes:,}"
            node_info.append(info)
        
        # 创建节点轨迹
        node_trace = go.Scatter(
            x=node_x, y=node_y,
            mode='markers+text',
            hoverinfo='text',
            text=node_text,
            textposition="middle center",
            hovertext=node_info,
            marker=dict(
                showscale=True,
                colorscale='Viridis',
                reversescale=True,
                color=node_colors,
                size=node_sizes,
                colorbar=dict(
                    thickness=15,
                    len=0.5,
                    x=1.02,
                    title="流量字节数"
                ),
                line=dict(width=2, color='white')
            )
        )
        
        # 创建图表
        fig = go.Figure(data=[edge_trace, node_trace],
                       layout=go.Layout(
                           title='网络拓扑图',
                           titlefont_size=16,
                           showlegend=False,
                           hovermode='closest',
                           margin=dict(b=20,l=5,r=5,t=40),
                           annotations=[ dict(
                               text="节点大小表示连接数，颜色表示流量大小",
                               showarrow=False,
                               xref="paper", yref="paper",
                               x=0.005, y=-0.002,
                               xanchor='left', yanchor='bottom',
                               font=dict(color='gray', size=12)
                           )],
                           xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                           yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                           width=self.width,
                           height=self.height
                       ))
        
        return fig
    
    def _analyze_traffic_flows(self, packets: List[Packet]) -> Dict[str, Any]:
        """
        分析流量流向
        
        Args:
            packets: 数据包列表
            
        Returns:
            Dict[str, Any]: 流量流向数据
        """
        # 统计源IP、目标IP和协议的流量
        flows = defaultdict(lambda: defaultdict(int))
        
        for packet in packets:
            if packet.src_ip and packet.dst_ip and packet.protocol:
                protocol = packet.protocol.value
                flows[packet.src_ip][f"{packet.dst_ip}_{protocol}"] += packet.size
        
        return dict(flows)
    
    def _create_sankey_diagram(self, flow_data: Dict[str, Any]) -> go.Figure:
        """
        创建Sankey流量图
        
        Args:
            flow_data: 流量流向数据
            
        Returns:
            go.Figure: Plotly图表对象
        """
        # 准备Sankey图数据
        all_nodes = set()
        links = []
        
        # 收集所有节点
        for src, targets in flow_data.items():
            all_nodes.add(src)
            for target_proto, bytes_count in targets.items():
                target, protocol = target_proto.rsplit('_', 1)
                all_nodes.add(target)
        
        # 创建节点索引映射
        node_list = list(all_nodes)
        node_indices = {node: i for i, node in enumerate(node_list)}
        
        # 创建链接
        for src, targets in flow_data.items():
            src_idx = node_indices[src]
            for target_proto, bytes_count in targets.items():
                target, protocol = target_proto.rsplit('_', 1)
                dst_idx = node_indices[target]
                
                if bytes_count > 0:  # 只显示有流量的连接
                    links.append({
                        'source': src_idx,
                        'target': dst_idx,
                        'value': bytes_count
                    })
        
        # 创建Sankey图
        fig = go.Figure(data=[go.Sankey(
            node=dict(
                pad=15,
                thickness=20,
                line=dict(color="black", width=0.5),
                label=node_list,
                color="lightblue"
            ),
            link=dict(
                source=[link['source'] for link in links],
                target=[link['target'] for link in links],
                value=[link['value'] for link in links]
            )
        )])
        
        fig.update_layout(
            title_text="网络流量流向图",
            font_size=10,
            width=self.width,
            height=self.height
        )
        
        return fig
    
    def get_network_statistics(self, packets: List[Packet]) -> Dict[str, Any]:
        """
        获取网络拓扑统计信息
        
        Args:
            packets: 数据包列表
            
        Returns:
            Dict[str, Any]: 网络统计信息
        """
        if not NETWORKX_AVAILABLE:
            return {}
        
        try:
            G = self._build_network_graph(packets)
            
            stats = {
                'node_count': G.number_of_nodes(),
                'edge_count': G.number_of_edges(),
                'density': nx.density(G),
                'is_connected': nx.is_connected(G),
                'average_clustering': nx.average_clustering(G),
                'diameter': nx.diameter(G) if nx.is_connected(G) else 0
            }
            
            # 中心性分析
            if G.number_of_nodes() > 0:
                degree_centrality = nx.degree_centrality(G)
                betweenness_centrality = nx.betweenness_centrality(G)
                
                stats['most_connected_node'] = max(degree_centrality, key=degree_centrality.get)
                stats['most_central_node'] = max(betweenness_centrality, key=betweenness_centrality.get)
            
            return stats
            
        except Exception as e:
            self.logger.error(f"计算网络统计信息失败: {str(e)}")
            return {}
