#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CLI命令实现
CLI Commands Implementation

实现各种CLI命令的具体功能，包括分析、可视化、
服务器启动、配置管理等命令。
"""

import logging
import json
import os
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime

from ..core.analyzer import NetworkAnalyzer, AnalysisResult
from ..core.models import Packet, ProtocolType, PacketDirection
from ..ai import ContextAwareAnalyzer, AnalysisContext, AnalysisType
from ..visualization import (
    TimeSeriesVisualizer, ProtocolChartVisualizer,
    NetworkTopologyVisualizer, InteractiveChartManager
)


def analyze_command(input_file: str, output: Optional[str] = None,
                   format: str = 'json', enable_ai: bool = False,
                   enable_viz: bool = False, analysis_type: str = 'comprehensive',
                   verbose: bool = False, save_results: bool = True) -> Dict[str, Any]:
    """
    执行数据包分析命令
    
    Args:
        input_file: 输入文件路径
        output: 输出文件路径
        format: 输出格式
        enable_ai: 是否启用AI分析
        enable_viz: 是否启用可视化
        analysis_type: 分析类型
        verbose: 详细模式
        save_results: 是否保存结果到文件

    Returns:
        Dict[str, Any]: 命令执行结果
    """
    logger = logging.getLogger("analyze_command")
    
    try:
        logger.info(f"开始分析文件: {input_file}")

        # 验证输入文件
        input_path = Path(input_file)
        if not input_path.exists():
            return {
                'success': False,
                'error': f'输入文件不存在: {input_file}'
            }

        # 检查文件格式
        allowed_extensions = {'.pcap', '.pcapng', '.cap', '.dmp'}
        if input_path.suffix.lower() not in allowed_extensions:
            return {
                'success': False,
                'error': f'不支持的文件格式: {input_path.suffix}。支持的格式: {", ".join(allowed_extensions)}'
            }

        # 检查文件大小
        file_size = input_path.stat().st_size
        max_size = 500 * 1024 * 1024  # 500MB
        if file_size > max_size:
            return {
                'success': False,
                'error': f'文件过大: {file_size / 1024 / 1024:.1f}MB。最大支持: {max_size / 1024 / 1024}MB'
            }

        logger.info(f"文件验证通过: {input_path.name} ({file_size / 1024:.1f} KB)")

        # 创建分析器
        analyzer = NetworkAnalyzer({
            'enable_geo_analysis': True,
            'top_n_limit': 20,
            'anomaly_threshold': 3.0
        })

        # 创建演示数据包（实际应该从PCAP文件解析）
        packets = _create_demo_packets_from_file(input_file)

        # 添加文件元数据
        file_metadata = {
            'filename': input_path.name,
            'file_size': file_size,
            'file_type': input_path.suffix.lower(),
            'analysis_start_time': datetime.now().isoformat()
        }
        
        if not packets:
            return {
                'success': False,
                'error': '无法从文件中读取数据包'
            }
        
        # 执行基础分析
        result = AnalysisResult(f"cli_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        result.protocol_stats = analyzer.analyze_protocols(packets)
        result.traffic_stats = analyzer.analyze_traffic(packets)
        result.anomalies = analyzer.detect_anomalies(packets)
        result.security_threats = analyzer.detect_security_threats(packets)
        
        # AI分析（如果启用）
        ai_insights = None
        if enable_ai:
            try:
                ai_analyzer = ContextAwareAnalyzer({'provider': 'mock'})
                context = AnalysisContext()
                ai_result = ai_analyzer.analyze_with_context(
                    result.to_dict(), context, AnalysisType(analysis_type)
                )
                ai_insights = ai_result.get('ai_analysis', '')
                logger.info("AI分析完成")
            except Exception as e:
                logger.warning(f"AI分析失败: {e}")
        
        # 生成输出文件路径
        if not output:
            input_path = Path(input_file)
            output = input_path.parent / f"{input_path.stem}_analysis.{format}"
        
        # 准备结果数据
        result_data = result.to_dict()
        result_data['file_metadata'] = file_metadata
        result_data['analysis_config'] = {
            'analysis_type': analysis_type,
            'ai_enabled': enable_ai,
            'visualization_enabled': enable_viz,
            'format': format
        }

        if ai_insights:
            result_data['ai_insights'] = ai_insights

        # 保存结果（如果启用）
        if save_results:
            if format == 'json':
                with open(output, 'w', encoding='utf-8') as f:
                    json.dump(result_data, f, ensure_ascii=False, indent=2, default=str)
            elif format == 'html':
                _save_html_report(result_data, output)
            elif format == 'csv':
                _save_csv_report(result_data, output)

            # 同时保存到results目录（用于Web界面）
            results_dir = Path("results")
            results_dir.mkdir(exist_ok=True)
            result_file = results_dir / f"{result.analysis_id}.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2, default=str)
        
        # 生成摘要
        summary = result.get_summary()
        
        logger.info(f"分析完成，结果已保存到: {output}")
        
        return {
            'success': True,
            'output_file': str(output),
            'summary': summary,
            'ai_enabled': enable_ai,
            'viz_enabled': enable_viz
        }
        
    except Exception as e:
        logger.error(f"分析命令执行失败: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


def analyze_file_detailed(input_file: str, verbose: bool = False) -> Dict[str, Any]:
    """
    详细的单文件分析

    Args:
        input_file: 输入文件路径
        verbose: 详细模式

    Returns:
        Dict[str, Any]: 详细分析结果
    """
    logger = logging.getLogger("analyze_file_detailed")

    try:
        # 基础分析
        basic_result = analyze_command(
            input_file=input_file,
            enable_ai=True,
            enable_viz=True,
            analysis_type='comprehensive',
            verbose=verbose,
            save_results=True
        )

        if not basic_result['success']:
            return basic_result

        # 添加详细的文件信息
        input_path = Path(input_file)
        file_stats = input_path.stat()

        detailed_info = {
            'file_analysis': {
                'absolute_path': str(input_path.absolute()),
                'file_size_bytes': file_stats.st_size,
                'file_size_mb': file_stats.st_size / 1024 / 1024,
                'created_time': datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                'modified_time': datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
                'file_extension': input_path.suffix.lower(),
                'is_readable': os.access(input_path, os.R_OK)
            },
            'analysis_performance': {
                'analysis_duration': '< 1秒',  # 实际应该测量
                'memory_usage': 'N/A',
                'cpu_usage': 'N/A'
            }
        }

        # 合并结果
        basic_result['detailed_info'] = detailed_info

        logger.info(f"详细分析完成: {input_file}")
        return basic_result

    except Exception as e:
        logger.error(f"详细分析失败: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


def batch_analyze_command(input_dir: str, output_dir: Optional[str] = None,
                         pattern: str = "*.pcap", format: str = 'json',
                         enable_ai: bool = False, enable_viz: bool = False,
                         analysis_type: str = 'comprehensive',
                         max_workers: int = 4, verbose: bool = False) -> Dict[str, Any]:
    """
    批量分析命令

    Args:
        input_dir: 输入目录路径
        output_dir: 输出目录路径
        pattern: 文件匹配模式
        format: 输出格式
        enable_ai: 是否启用AI分析
        enable_viz: 是否启用可视化
        analysis_type: 分析类型
        max_workers: 最大并发数
        verbose: 详细模式

    Returns:
        Dict[str, Any]: 批处理结果
    """
    logger = logging.getLogger("batch_analyze_command")

    try:
        logger.info(f"开始批量分析: {input_dir}")

        # 验证输入目录
        input_path = Path(input_dir)
        if not input_path.exists():
            return {
                'success': False,
                'error': f'输入目录不存在: {input_dir}'
            }

        if not input_path.is_dir():
            return {
                'success': False,
                'error': f'输入路径不是目录: {input_dir}'
            }

        # 查找匹配的文件
        files = list(input_path.glob(pattern))
        if not files:
            return {
                'success': False,
                'error': f'在目录 {input_dir} 中未找到匹配 {pattern} 的文件'
            }

        logger.info(f"找到 {len(files)} 个文件待处理")

        # 设置输出目录
        if not output_dir:
            output_path = input_path / "analysis_results"
        else:
            output_path = Path(output_dir)

        output_path.mkdir(exist_ok=True)

        # 批处理结果
        batch_results = {
            'total_files': len(files),
            'processed_files': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'results': [],
            'errors': [],
            'start_time': datetime.now().isoformat()
        }

        # 处理每个文件
        for i, file_path in enumerate(files, 1):
            try:
                logger.info(f"处理文件 {i}/{len(files)}: {file_path.name}")

                # 生成输出文件路径
                output_file = output_path / f"{file_path.stem}_analysis.{format}"

                # 分析单个文件
                result = analyze_command(
                    input_file=str(file_path),
                    output=str(output_file),
                    format=format,
                    enable_ai=enable_ai,
                    enable_viz=enable_viz,
                    analysis_type=analysis_type,
                    verbose=verbose
                )

                batch_results['processed_files'] += 1

                if result['success']:
                    batch_results['successful_analyses'] += 1
                    batch_results['results'].append({
                        'file': str(file_path),
                        'output': str(output_file),
                        'status': 'success',
                        'summary': result.get('summary', {})
                    })
                    logger.info(f"✅ 成功处理: {file_path.name}")
                else:
                    batch_results['failed_analyses'] += 1
                    error_info = {
                        'file': str(file_path),
                        'error': result.get('error', '未知错误'),
                        'status': 'failed'
                    }
                    batch_results['errors'].append(error_info)
                    logger.error(f"❌ 处理失败: {file_path.name} - {error_info['error']}")

            except Exception as e:
                batch_results['failed_analyses'] += 1
                error_info = {
                    'file': str(file_path),
                    'error': str(e),
                    'status': 'error'
                }
                batch_results['errors'].append(error_info)
                logger.error(f"❌ 处理异常: {file_path.name} - {str(e)}")

        # 完成时间
        batch_results['end_time'] = datetime.now().isoformat()
        batch_results['duration'] = '计算中...'  # 实际应该计算时间差

        # 保存批处理报告
        report_file = output_path / "batch_analysis_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(batch_results, f, ensure_ascii=False, indent=2, default=str)

        logger.info(f"批量分析完成: 成功 {batch_results['successful_analyses']}, 失败 {batch_results['failed_analyses']}")

        return {
            'success': True,
            'batch_results': batch_results,
            'output_dir': str(output_path),
            'report_file': str(report_file)
        }

    except Exception as e:
        logger.error(f"批量分析失败: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


def visualize_command(input_file: str, output_dir: Optional[str] = None,
                     chart_types: List[str] = None, format: str = 'html',
                     verbose: bool = False) -> Dict[str, Any]:
    """
    执行可视化命令
    
    Args:
        input_file: 输入文件路径（分析结果或PCAP文件）
        output_dir: 输出目录
        chart_types: 图表类型列表
        format: 输出格式
        verbose: 详细模式
        
    Returns:
        Dict[str, Any]: 命令执行结果
    """
    logger = logging.getLogger("visualize_command")
    
    try:
        logger.info(f"开始生成可视化图表: {input_file}")
        
        # 设置默认图表类型
        if not chart_types:
            chart_types = ['timeline', 'protocol']
        
        # 设置输出目录
        if not output_dir:
            input_path = Path(input_file)
            output_dir = input_path.parent / f"{input_path.stem}_charts"
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # 创建可视化器
        visualizers = {
            'timeline': TimeSeriesVisualizer(),
            'protocol': ProtocolChartVisualizer(),
            'topology': NetworkTopologyVisualizer(),
            'dashboard': InteractiveChartManager()
        }
        
        # 获取数据
        if input_file.endswith('.json'):
            # 从分析结果文件读取
            with open(input_file, 'r', encoding='utf-8') as f:
                analysis_data = json.load(f)
            packets = _create_demo_packets()  # 简化实现
        else:
            # 从PCAP文件读取
            packets = _create_demo_packets_from_file(input_file)
            analyzer = NetworkAnalyzer()
            protocol_stats = analyzer.analyze_protocols(packets)
            analysis_data = {'protocol_stats': protocol_stats}
        
        # 生成图表
        generated_charts = []
        
        for chart_type in chart_types:
            try:
                if chart_type == 'timeline' and 'timeline' in visualizers:
                    fig = visualizers['timeline'].create_traffic_timeline(packets)
                    if fig:
                        chart_path = output_path / f"timeline.{format}"
                        if format == 'html':
                            fig.write_html(str(chart_path))
                        else:
                            fig.write_image(str(chart_path))
                        generated_charts.append({
                            'type': 'timeline',
                            'path': str(chart_path)
                        })
                
                elif chart_type == 'protocol' and 'protocol' in visualizers:
                    protocol_stats = analysis_data.get('protocol_stats')
                    if protocol_stats:
                        fig = visualizers['protocol'].create_protocol_pie_chart(protocol_stats)
                        if fig:
                            chart_path = output_path / f"protocol.{format}"
                            if format == 'html':
                                fig.write_html(str(chart_path))
                            else:
                                fig.write_image(str(chart_path))
                            generated_charts.append({
                                'type': 'protocol',
                                'path': str(chart_path)
                            })
                
                logger.info(f"生成图表: {chart_type}")
                
            except Exception as e:
                logger.warning(f"生成{chart_type}图表失败: {e}")
        
        logger.info(f"可视化完成，图表已保存到: {output_dir}")
        
        return {
            'success': True,
            'output_dir': str(output_dir),
            'charts': generated_charts,
            'format': format
        }
        
    except Exception as e:
        logger.error(f"可视化命令执行失败: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


def server_command(host: str = '127.0.0.1', port: int = 8000,
                  reload: bool = False, workers: int = 1,
                  verbose: bool = False) -> None:
    """
    启动Web服务器命令
    
    Args:
        host: 服务器主机地址
        port: 服务器端口
        reload: 是否启用自动重载
        workers: 工作进程数
        verbose: 详细模式
    """
    logger = logging.getLogger("server_command")
    
    try:
        # 尝试导入uvicorn
        import uvicorn
        from ..web.api import create_app
        
        # 创建应用
        app = create_app({
            'debug': verbose,
            'title': '网络数据包分析工具',
            'version': '1.0.0'
        })
        
        # 启动服务器
        uvicorn.run(
            app,
            host=host,
            port=port,
            reload=reload,
            workers=workers if not reload else 1,
            log_level="debug" if verbose else "info"
        )
        
    except ImportError:
        logger.error("uvicorn库未安装，无法启动Web服务器")
        raise
    except Exception as e:
        logger.error(f"服务器启动失败: {str(e)}")
        raise


def config_command(show: bool = False, set_config: Dict[str, str] = None,
                  reset: bool = False, verbose: bool = False) -> Dict[str, Any]:
    """
    配置管理命令
    
    Args:
        show: 显示当前配置
        set_config: 设置配置项
        reset: 重置配置
        verbose: 详细模式
        
    Returns:
        Dict[str, Any]: 命令执行结果
    """
    logger = logging.getLogger("config_command")
    
    # 默认配置
    default_config = {
        'ai.provider': 'mock',
        'ai.model': 'gpt-3.5-turbo',
        'visualization.theme': 'plotly',
        'analysis.enable_geo': False,
        'analysis.top_n_limit': 10,
        'server.host': '127.0.0.1',
        'server.port': 8000
    }
    
    # 配置文件路径
    config_file = Path.home() / '.netanalysis' / 'config.json'
    config_file.parent.mkdir(exist_ok=True)
    
    try:
        # 读取现有配置
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                current_config = json.load(f)
        else:
            current_config = default_config.copy()
        
        # 重置配置
        if reset:
            current_config = default_config.copy()
            logger.info("配置已重置为默认值")
        
        # 设置配置项
        if set_config:
            current_config.update(set_config)
            logger.info(f"更新配置项: {list(set_config.keys())}")
        
        # 保存配置
        if set_config or reset:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(current_config, f, ensure_ascii=False, indent=2)
        
        return {
            'success': True,
            'config': current_config,
            'config_file': str(config_file)
        }
        
    except Exception as e:
        logger.error(f"配置命令执行失败: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


def export_command(input_file: str, output: Optional[str] = None,
                  format: str = 'csv', include: List[str] = None,
                  verbose: bool = False) -> Dict[str, Any]:
    """
    导出命令
    
    Args:
        input_file: 输入文件路径
        output: 输出文件路径
        format: 导出格式
        include: 包含的数据类型
        verbose: 详细模式
        
    Returns:
        Dict[str, Any]: 命令执行结果
    """
    logger = logging.getLogger("export_command")
    
    try:
        logger.info(f"开始导出数据: {input_file}")
        
        # 设置默认包含项
        if not include:
            include = ['protocols', 'traffic']
        
        # 读取分析结果
        if input_file.endswith('.json'):
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        else:
            return {
                'success': False,
                'error': '不支持的输入文件格式'
            }
        
        # 生成输出文件路径
        if not output:
            input_path = Path(input_file)
            output = input_path.parent / f"{input_path.stem}_export.{format}"
        
        # 导出数据
        if format == 'csv':
            _export_to_csv(data, output, include)
        elif format == 'excel':
            _export_to_excel(data, output, include)
        elif format == 'json':
            _export_to_json(data, output, include)
        elif format == 'pdf':
            _export_to_pdf(data, output, include)
        else:
            return {
                'success': False,
                'error': f'不支持的导出格式: {format}'
            }
        
        logger.info(f"导出完成，文件已保存到: {output}")
        
        return {
            'success': True,
            'output_file': str(output),
            'format': format,
            'included': include
        }
        
    except Exception as e:
        logger.error(f"导出命令执行失败: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


# 辅助函数
def _create_demo_packets_from_file(file_path: str) -> List[Packet]:
    """从文件创建演示数据包（简化实现）"""
    # 这里应该实现PCAP文件解析
    # 目前返回演示数据
    return _create_demo_packets()


def _create_demo_packets() -> List[Packet]:
    """创建演示数据包"""
    base_time = datetime.now()
    packets = []
    
    for i in range(100):
        packets.append(Packet(
            timestamp=base_time,
            size=64 + i * 5,
            src_ip=f"192.168.1.{100 + i % 20}",
            dst_ip=f"10.0.0.{1 + i % 10}",
            src_port=12345 + i,
            dst_port=80 if i % 3 == 0 else 443,
            protocol=ProtocolType.TCP if i % 2 == 0 else ProtocolType.UDP,
            direction=PacketDirection.OUTBOUND if i % 2 == 0 else PacketDirection.INBOUND
        ))
    
    return packets


def _save_html_report(data: Dict[str, Any], output_path: str) -> None:
    """保存HTML报告"""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>网络分析报告</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>网络数据包分析报告</h1>
        <pre>{json.dumps(data, ensure_ascii=False, indent=2, default=str)}</pre>
    </body>
    </html>
    """
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)


def _save_csv_report(data: Dict[str, Any], output_path: str) -> None:
    """保存CSV报告"""
    import csv
    
    with open(output_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['指标', '数值'])
        
        # 写入基础统计
        if 'protocol_stats' in data:
            stats = data['protocol_stats']
            writer.writerow(['总包数', stats.get('total_packets', 0)])
            writer.writerow(['总字节数', stats.get('total_bytes', 0)])


def _export_to_csv(data: Dict[str, Any], output_path: str, include: List[str]) -> None:
    """导出为CSV格式"""
    _save_csv_report(data, output_path)


def _export_to_excel(data: Dict[str, Any], output_path: str, include: List[str]) -> None:
    """导出为Excel格式"""
    # 简化实现，实际应该使用pandas或openpyxl
    _save_csv_report(data, output_path.replace('.xlsx', '.csv'))


def _export_to_json(data: Dict[str, Any], output_path: str, include: List[str]) -> None:
    """导出为JSON格式"""
    filtered_data = {}
    for key in include:
        if key in data:
            filtered_data[key] = data[key]
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(filtered_data, f, ensure_ascii=False, indent=2, default=str)


def _export_to_pdf(data: Dict[str, Any], output_path: str, include: List[str]) -> None:
    """导出为PDF格式"""
    # 简化实现，实际应该使用reportlab等库
    html_path = output_path.replace('.pdf', '.html')
    _save_html_report(data, html_path)


def export_comprehensive_report(analysis_results: List[Dict[str, Any]],
                               output_path: str, format: str = 'html') -> Dict[str, Any]:
    """
    导出综合分析报告

    Args:
        analysis_results: 分析结果列表
        output_path: 输出路径
        format: 导出格式

    Returns:
        Dict[str, Any]: 导出结果
    """
    logger = logging.getLogger("export_comprehensive_report")

    try:
        logger.info(f"开始生成综合报告: {output_path}")

        # 汇总统计
        total_packets = sum(result.get('protocol_stats', {}).get('total_packets', 0)
                           for result in analysis_results)
        total_bytes = sum(result.get('protocol_stats', {}).get('total_bytes', 0)
                         for result in analysis_results)
        total_flows = sum(result.get('protocol_stats', {}).get('unique_flows', 0)
                         for result in analysis_results)

        # 协议汇总
        protocol_summary = {}
        for result in analysis_results:
            protocol_counts = result.get('protocol_stats', {}).get('protocol_counts', {})
            for protocol, count in protocol_counts.items():
                protocol_summary[protocol] = protocol_summary.get(protocol, 0) + count

        # 生成报告数据
        report_data = {
            'report_info': {
                'title': '网络数据包分析综合报告',
                'generated_at': datetime.now().isoformat(),
                'total_analyses': len(analysis_results),
                'report_format': format
            },
            'executive_summary': {
                'total_packets_analyzed': total_packets,
                'total_bytes_analyzed': total_bytes,
                'total_flows_analyzed': total_flows,
                'unique_protocols': len(protocol_summary),
                'analysis_period': 'N/A'  # 应该从实际数据计算
            },
            'protocol_summary': protocol_summary,
            'detailed_results': analysis_results[:10],  # 限制详细结果数量
            'recommendations': _generate_recommendations(analysis_results),
            'appendix': {
                'methodology': '基于网络数据包深度分析',
                'tools_used': ['NetworkAnalyzer', 'AI分析引擎', '可视化组件'],
                'data_sources': [result.get('file_metadata', {}).get('filename', 'Unknown')
                               for result in analysis_results]
            }
        }

        # 根据格式导出
        if format == 'html':
            _export_html_comprehensive_report(report_data, output_path)
        elif format == 'json':
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)
        elif format == 'csv':
            _export_csv_comprehensive_report(report_data, output_path)
        else:
            return {
                'success': False,
                'error': f'不支持的导出格式: {format}'
            }

        logger.info(f"综合报告生成完成: {output_path}")

        return {
            'success': True,
            'output_file': output_path,
            'report_data': report_data,
            'format': format
        }

    except Exception as e:
        logger.error(f"生成综合报告失败: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


def _generate_recommendations(analysis_results: List[Dict[str, Any]]) -> List[str]:
    """基于分析结果生成建议"""
    recommendations = []

    # 分析异常数量
    total_anomalies = sum(len(result.get('anomalies', [])) for result in analysis_results)
    if total_anomalies > 0:
        recommendations.append(f"检测到 {total_anomalies} 个网络异常，建议进一步调查")

    # 分析威胁数量
    total_threats = sum(len(result.get('security_threats', [])) for result in analysis_results)
    if total_threats > 0:
        recommendations.append(f"发现 {total_threats} 个安全威胁，建议立即处理")

    # 协议分析
    protocol_counts = {}
    for result in analysis_results:
        for protocol, count in result.get('protocol_stats', {}).get('protocol_counts', {}).items():
            protocol_counts[protocol] = protocol_counts.get(protocol, 0) + count

    if protocol_counts:
        dominant_protocol = max(protocol_counts, key=protocol_counts.get)
        recommendations.append(f"主要协议为 {dominant_protocol.upper()}，占总流量的主要部分")

    # 通用建议
    recommendations.extend([
        "建议定期进行网络流量分析以维护网络安全",
        "考虑实施实时监控系统以及时发现异常",
        "建议对检测到的异常进行深入分析"
    ])

    return recommendations


def _export_html_comprehensive_report(report_data: Dict[str, Any], output_path: str) -> None:
    """导出HTML格式的综合报告"""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>{report_data['report_info']['title']}</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 20px;
                background: #f5f5f5;
                line-height: 1.6;
            }}
            .container {{
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                overflow: hidden;
            }}
            .header {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                text-align: center;
            }}
            .content {{ padding: 30px; }}
            .section {{ margin: 30px 0; }}
            .section h2 {{
                color: #333;
                border-bottom: 2px solid #667eea;
                padding-bottom: 10px;
            }}
            .stats-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin: 20px 0;
            }}
            .stat-card {{
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                border-left: 4px solid #667eea;
            }}
            .stat-number {{ font-size: 2em; font-weight: bold; color: #667eea; }}
            .stat-label {{ color: #666; margin-top: 5px; }}
            table {{
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }}
            th, td {{
                padding: 12px;
                text-align: left;
                border-bottom: 1px solid #dee2e6;
            }}
            th {{
                background: #f8f9fa;
                font-weight: 600;
            }}
            .recommendations {{
                background: #e7f3ff;
                padding: 20px;
                border-radius: 8px;
                border-left: 4px solid #007bff;
            }}
            .recommendations ul {{ margin: 10px 0; }}
            .recommendations li {{ margin: 5px 0; }}
            .footer {{
                background: #f8f9fa;
                padding: 20px;
                text-align: center;
                color: #666;
                border-top: 1px solid #dee2e6;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>{report_data['report_info']['title']}</h1>
                <p>生成时间: {report_data['report_info']['generated_at']}</p>
                <p>分析文件数: {report_data['report_info']['total_analyses']}</p>
            </div>

            <div class="content">
                <div class="section">
                    <h2>📊 执行摘要</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">{report_data['executive_summary']['total_packets_analyzed']:,}</div>
                            <div class="stat-label">总数据包数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{report_data['executive_summary']['total_bytes_analyzed']:,}</div>
                            <div class="stat-label">总字节数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{report_data['executive_summary']['total_flows_analyzed']:,}</div>
                            <div class="stat-label">总流数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{report_data['executive_summary']['unique_protocols']}</div>
                            <div class="stat-label">协议种类</div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h2>🌐 协议分布</h2>
                    <table>
                        <thead>
                            <tr>
                                <th>协议</th>
                                <th>数据包数</th>
                                <th>占比</th>
                            </tr>
                        </thead>
                        <tbody>
    """

    # 添加协议分布表格
    total_protocol_packets = sum(report_data['protocol_summary'].values())
    for protocol, count in sorted(report_data['protocol_summary'].items(),
                                 key=lambda x: x[1], reverse=True):
        percentage = (count / total_protocol_packets * 100) if total_protocol_packets > 0 else 0
        html_content += f"""
                            <tr>
                                <td>{protocol.upper()}</td>
                                <td>{count:,}</td>
                                <td>{percentage:.1f}%</td>
                            </tr>
        """

    html_content += f"""
                        </tbody>
                    </table>
                </div>

                <div class="section">
                    <h2>💡 建议和建议</h2>
                    <div class="recommendations">
                        <ul>
    """

    # 添加建议列表
    for recommendation in report_data['recommendations']:
        html_content += f"<li>{recommendation}</li>"

    html_content += f"""
                        </ul>
                    </div>
                </div>

                <div class="section">
                    <h2>📋 附录</h2>
                    <p><strong>分析方法:</strong> {report_data['appendix']['methodology']}</p>
                    <p><strong>使用工具:</strong> {', '.join(report_data['appendix']['tools_used'])}</p>
                    <p><strong>数据源:</strong> {len(report_data['appendix']['data_sources'])} 个文件</p>
                </div>
            </div>

            <div class="footer">
                <p>网络数据包分析工具 v1.0.0 | 生成于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
        </div>
    </body>
    </html>
    """

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)


def _export_csv_comprehensive_report(report_data: Dict[str, Any], output_path: str) -> None:
    """导出CSV格式的综合报告"""
    import csv

    with open(output_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)

        # 写入摘要信息
        writer.writerow(['报告信息'])
        writer.writerow(['标题', report_data['report_info']['title']])
        writer.writerow(['生成时间', report_data['report_info']['generated_at']])
        writer.writerow(['分析文件数', report_data['report_info']['total_analyses']])
        writer.writerow([])

        # 写入执行摘要
        writer.writerow(['执行摘要'])
        writer.writerow(['指标', '数值'])
        for key, value in report_data['executive_summary'].items():
            writer.writerow([key, value])
        writer.writerow([])

        # 写入协议分布
        writer.writerow(['协议分布'])
        writer.writerow(['协议', '数据包数'])
        for protocol, count in report_data['protocol_summary'].items():
            writer.writerow([protocol.upper(), count])
        writer.writerow([])

        # 写入建议
        writer.writerow(['建议'])
        for i, recommendation in enumerate(report_data['recommendations'], 1):
            writer.writerow([f'建议{i}', recommendation])
