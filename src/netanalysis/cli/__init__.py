#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命令行工具模块
Command Line Interface Module

提供功能完整的CLI工具，支持文件分析、批量处理、
配置管理等命令行操作。
"""

from .main import main, create_cli
from .commands import (
    analyze_command, visualize_command, server_command,
    config_command, export_command
)

# 检查依赖库可用性
try:
    import click
    CLICK_AVAILABLE = True
except ImportError:
    CLICK_AVAILABLE = False

try:
    import rich
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

__all__ = [
    'main',
    'create_cli',
    'analyze_command',
    'visualize_command',
    'server_command',
    'config_command',
    'export_command',
    'CLICK_AVAILABLE',
    'RICH_AVAILABLE'
]