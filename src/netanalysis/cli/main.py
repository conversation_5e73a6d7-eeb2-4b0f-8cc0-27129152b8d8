#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CLI主程序
CLI Main Program

实现命令行工具的主入口，提供完整的CLI功能，
包括文件分析、可视化、服务器启动等命令。
"""

import logging
import sys
from typing import Optional, Dict, Any
from pathlib import Path

try:
    import click
    from rich.console import Console
    from rich.logging import RichHandler
    from rich.table import Table
    from rich.panel import Panel
    from rich.text import Text
    CLI_AVAILABLE = True
except ImportError:
    CLI_AVAILABLE = False
    print("警告: Click或Rich库未安装，CLI功能将受限")

from ..core.analyzer import NetworkAnalyzer
from ..core.models import Packet
from ..ai import ContextAwareAnalyzer, AnalysisContext
from ..visualization import TimeSeriesVisualizer, ProtocolChartVisualizer
from ..web.api import create_app


# 全局控制台对象
console = Console() if CLI_AVAILABLE else None


def setup_logging(verbose: bool = False, quiet: bool = False) -> None:
    """
    设置日志配置
    
    Args:
        verbose: 详细模式
        quiet: 安静模式
    """
    if quiet:
        level = logging.ERROR
    elif verbose:
        level = logging.DEBUG
    else:
        level = logging.INFO
    
    if CLI_AVAILABLE:
        # 使用Rich的日志处理器
        logging.basicConfig(
            level=level,
            format="%(message)s",
            datefmt="[%X]",
            handlers=[RichHandler(console=console, rich_tracebacks=True)]
        )
    else:
        # 使用标准日志处理器
        logging.basicConfig(
            level=level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )


@click.group()
@click.option('--verbose', '-v', is_flag=True, help='启用详细输出')
@click.option('--quiet', '-q', is_flag=True, help='启用安静模式')
@click.option('--config', '-c', type=click.Path(exists=True), help='配置文件路径')
@click.pass_context
def cli(ctx: click.Context, verbose: bool, quiet: bool, config: Optional[str]):
    """
    🚀 网络数据包分析工具
    
    基于AI的网络数据包分析和可视化平台
    """
    if not CLI_AVAILABLE:
        click.echo("错误: 缺少必要的依赖库 (click, rich)")
        sys.exit(1)
    
    # 设置日志
    setup_logging(verbose, quiet)
    
    # 初始化上下文
    ctx.ensure_object(dict)
    ctx.obj['verbose'] = verbose
    ctx.obj['quiet'] = quiet
    ctx.obj['config'] = config
    
    # 显示欢迎信息
    if not quiet:
        console.print(Panel.fit(
            "[bold blue]网络数据包分析工具[/bold blue]\n"
            "[dim]基于AI的网络分析和可视化平台[/dim]",
            border_style="blue"
        ))


@cli.command()
@click.argument('input_file', type=click.Path(exists=True))
@click.option('--output', '-o', type=click.Path(), help='输出文件路径')
@click.option('--format', '-f', type=click.Choice(['json', 'html', 'csv']), 
              default='json', help='输出格式')
@click.option('--enable-ai', is_flag=True, help='启用AI分析')
@click.option('--enable-viz', is_flag=True, help='启用可视化')
@click.option('--analysis-type', type=click.Choice(['comprehensive', 'security', 'performance']),
              default='comprehensive', help='分析类型')
@click.pass_context
def analyze(ctx: click.Context, input_file: str, output: Optional[str], 
           format: str, enable_ai: bool, enable_viz: bool, analysis_type: str):
    """
    📊 分析网络数据包文件
    
    支持PCAP、PCAPNG等格式的网络数据包文件分析
    """
    try:
        from .commands import analyze_command
    except ImportError:
        # 如果commands模块不存在，创建一个简单的实现
        def analyze_command(*args, **kwargs):
            return {'success': False, 'error': 'Commands module not implemented'}
    
    try:
        with console.status("[bold green]正在分析数据包...") as status:
            result = analyze_command(
                input_file=input_file,
                output=output,
                format=format,
                enable_ai=enable_ai,
                enable_viz=enable_viz,
                analysis_type=analysis_type,
                verbose=ctx.obj['verbose']
            )
        
        if result['success']:
            console.print(f"✅ 分析完成！结果已保存到: {result['output_file']}")
            
            # 显示分析摘要
            summary = result.get('summary', {})
            table = Table(title="分析摘要")
            table.add_column("指标", style="cyan")
            table.add_column("数值", style="green")
            
            for key, value in summary.items():
                table.add_row(key, str(value))
            
            console.print(table)
        else:
            console.print(f"❌ 分析失败: {result['error']}")
            sys.exit(1)
            
    except Exception as e:
        console.print(f"❌ 分析过程中发生错误: {str(e)}")
        if ctx.obj['verbose']:
            console.print_exception()
        sys.exit(1)


@cli.command()
@click.argument('input_dir', type=click.Path(exists=True))
@click.option('--output-dir', '-o', type=click.Path(), help='输出目录')
@click.option('--pattern', default='*.pcap', help='文件匹配模式')
@click.option('--format', '-f', type=click.Choice(['json', 'html', 'csv']),
              default='json', help='输出格式')
@click.option('--enable-ai', is_flag=True, help='启用AI分析')
@click.option('--enable-viz', is_flag=True, help='启用可视化')
@click.option('--analysis-type', type=click.Choice(['comprehensive', 'security', 'performance']),
              default='comprehensive', help='分析类型')
@click.option('--max-workers', default=4, help='最大并发数')
@click.pass_context
def batch(ctx: click.Context, input_dir: str, output_dir: Optional[str],
         pattern: str, format: str, enable_ai: bool, enable_viz: bool,
         analysis_type: str, max_workers: int):
    """
    📦 批量分析多个文件

    批量处理目录中的所有网络数据包文件
    """
    try:
        from .commands import batch_analyze_command
    except ImportError:
        def batch_analyze_command(*args, **kwargs):
            return {'success': False, 'error': 'Batch commands module not implemented'}

    try:
        with console.status("[bold green]正在批量分析文件...") as status:
            result = batch_analyze_command(
                input_dir=input_dir,
                output_dir=output_dir,
                pattern=pattern,
                format=format,
                enable_ai=enable_ai,
                enable_viz=enable_viz,
                analysis_type=analysis_type,
                max_workers=max_workers,
                verbose=ctx.obj['verbose']
            )

        if result['success']:
            batch_results = result['batch_results']
            console.print(f"✅ 批量分析完成！")
            console.print(f"   输出目录: {result['output_dir']}")
            console.print(f"   报告文件: {result['report_file']}")

            # 显示批处理摘要
            summary_table = Table(title="批处理摘要")
            summary_table.add_column("指标", style="cyan")
            summary_table.add_column("数值", style="green")

            summary_data = [
                ("总文件数", batch_results['total_files']),
                ("已处理", batch_results['processed_files']),
                ("成功分析", batch_results['successful_analyses']),
                ("失败分析", batch_results['failed_analyses']),
                ("成功率", f"{batch_results['successful_analyses']/batch_results['total_files']*100:.1f}%")
            ]

            for key, value in summary_data:
                summary_table.add_row(key, str(value))

            console.print(summary_table)

            # 显示错误信息（如果有）
            if batch_results['errors']:
                console.print(f"\n❌ 失败的文件:")
                for error in batch_results['errors']:
                    console.print(f"   • {Path(error['file']).name}: {error['error']}")
        else:
            console.print(f"❌ 批量分析失败: {result['error']}")
            sys.exit(1)

    except Exception as e:
        console.print(f"❌ 批量分析过程中发生错误: {str(e)}")
        if ctx.obj['verbose']:
            console.print_exception()
        sys.exit(1)


@cli.command()
@click.argument('input_file', type=click.Path(exists=True))
@click.option('--output-dir', '-o', type=click.Path(), help='输出目录')
@click.option('--chart-types', multiple=True, 
              type=click.Choice(['timeline', 'protocol', 'topology', 'dashboard']),
              default=['timeline', 'protocol'], help='图表类型')
@click.option('--format', type=click.Choice(['html', 'png', 'pdf']),
              default='html', help='输出格式')
@click.pass_context
def visualize(ctx: click.Context, input_file: str, output_dir: Optional[str],
             chart_types: tuple, format: str):
    """
    📈 生成可视化图表
    
    从分析结果生成各种类型的可视化图表
    """
    from .commands import visualize_command
    
    try:
        with console.status("[bold green]正在生成可视化图表...") as status:
            result = visualize_command(
                input_file=input_file,
                output_dir=output_dir,
                chart_types=list(chart_types),
                format=format,
                verbose=ctx.obj['verbose']
            )
        
        if result['success']:
            console.print(f"✅ 可视化完成！图表已保存到: {result['output_dir']}")
            
            # 显示生成的图表
            table = Table(title="生成的图表")
            table.add_column("图表类型", style="cyan")
            table.add_column("文件路径", style="green")
            
            for chart_info in result.get('charts', []):
                table.add_row(chart_info['type'], chart_info['path'])
            
            console.print(table)
        else:
            console.print(f"❌ 可视化失败: {result['error']}")
            sys.exit(1)
            
    except Exception as e:
        console.print(f"❌ 可视化过程中发生错误: {str(e)}")
        if ctx.obj['verbose']:
            console.print_exception()
        sys.exit(1)


@cli.command()
@click.option('--host', default='127.0.0.1', help='服务器主机地址')
@click.option('--port', default=8000, help='服务器端口')
@click.option('--reload', is_flag=True, help='启用自动重载')
@click.option('--workers', default=1, help='工作进程数')
@click.pass_context
def server(ctx: click.Context, host: str, port: int, reload: bool, workers: int):
    """
    🌐 启动Web服务器
    
    启动FastAPI Web服务器，提供Web界面和API接口
    """
    from .commands import server_command
    
    try:
        console.print(f"🚀 启动Web服务器: http://{host}:{port}")
        console.print("按 Ctrl+C 停止服务器")
        
        server_command(
            host=host,
            port=port,
            reload=reload,
            workers=workers,
            verbose=ctx.obj['verbose']
        )
        
    except KeyboardInterrupt:
        console.print("\n👋 服务器已停止")
    except Exception as e:
        console.print(f"❌ 服务器启动失败: {str(e)}")
        if ctx.obj['verbose']:
            console.print_exception()
        sys.exit(1)


@cli.command()
@click.option('--show', is_flag=True, help='显示当前配置')
@click.option('--set', 'set_config', nargs=2, multiple=True, 
              metavar='KEY VALUE', help='设置配置项')
@click.option('--reset', is_flag=True, help='重置为默认配置')
@click.pass_context
def config(ctx: click.Context, show: bool, set_config: tuple, reset: bool):
    """
    ⚙️ 配置管理
    
    管理应用程序配置，包括AI设置、可视化选项等
    """
    from .commands import config_command
    
    try:
        result = config_command(
            show=show,
            set_config=dict(set_config) if set_config else {},
            reset=reset,
            verbose=ctx.obj['verbose']
        )
        
        if show or not set_config and not reset:
            # 显示配置
            table = Table(title="当前配置")
            table.add_column("配置项", style="cyan")
            table.add_column("值", style="green")
            
            for key, value in result.get('config', {}).items():
                table.add_row(key, str(value))
            
            console.print(table)
        
        if set_config:
            console.print("✅ 配置已更新")
        
        if reset:
            console.print("✅ 配置已重置为默认值")
            
    except Exception as e:
        console.print(f"❌ 配置操作失败: {str(e)}")
        if ctx.obj['verbose']:
            console.print_exception()
        sys.exit(1)


@cli.command()
@click.argument('input_file', type=click.Path(exists=True))
@click.option('--output', '-o', type=click.Path(), help='输出文件路径')
@click.option('--format', type=click.Choice(['csv', 'excel', 'json', 'pdf']),
              default='csv', help='导出格式')
@click.option('--include', multiple=True,
              type=click.Choice(['protocols', 'traffic', 'anomalies', 'threats']),
              default=['protocols', 'traffic'], help='包含的数据类型')
@click.pass_context
def export(ctx: click.Context, input_file: str, output: Optional[str],
          format: str, include: tuple):
    """
    📤 导出分析结果
    
    将分析结果导出为各种格式的报告
    """
    from .commands import export_command
    
    try:
        with console.status("[bold green]正在导出数据...") as status:
            result = export_command(
                input_file=input_file,
                output=output,
                format=format,
                include=list(include),
                verbose=ctx.obj['verbose']
            )
        
        if result['success']:
            console.print(f"✅ 导出完成！文件已保存到: {result['output_file']}")
        else:
            console.print(f"❌ 导出失败: {result['error']}")
            sys.exit(1)
            
    except Exception as e:
        console.print(f"❌ 导出过程中发生错误: {str(e)}")
        if ctx.obj['verbose']:
            console.print_exception()
        sys.exit(1)


@cli.command()
def version():
    """📋 显示版本信息"""
    # 检查依赖库状态
    dependencies = {}
    try:
        import plotly
        dependencies["Plotly"] = plotly.__version__
    except ImportError:
        dependencies["Plotly"] = "未安装"

    try:
        import fastapi
        dependencies["FastAPI"] = fastapi.__version__
    except ImportError:
        dependencies["FastAPI"] = "未安装"

    try:
        import networkx
        dependencies["NetworkX"] = networkx.__version__
    except ImportError:
        dependencies["NetworkX"] = "未安装"

    try:
        import pandas
        dependencies["Pandas"] = pandas.__version__
    except ImportError:
        dependencies["Pandas"] = "未安装"

    version_info = {
        "版本": "1.0.0",
        "Python版本": sys.version.split()[0],
        "平台": sys.platform,
        "工作目录": str(Path.cwd())
    }

    table = Table(title="版本信息")
    table.add_column("项目", style="cyan")
    table.add_column("值", style="green")

    for key, value in version_info.items():
        table.add_row(key, value)

    console.print(table)

    # 显示依赖库信息
    deps_table = Table(title="依赖库状态")
    deps_table.add_column("库名", style="cyan")
    deps_table.add_column("版本", style="green")

    for lib, ver in dependencies.items():
        deps_table.add_row(lib, ver)

    console.print(deps_table)


@cli.command()
@click.option('--check-deps', is_flag=True, help='检查依赖库')
@click.option('--check-config', is_flag=True, help='检查配置文件')
@click.option('--check-permissions', is_flag=True, help='检查文件权限')
def doctor(check_deps: bool, check_config: bool, check_permissions: bool):
    """🔧 系统诊断工具"""
    if not any([check_deps, check_config, check_permissions]):
        # 默认检查所有项目
        check_deps = check_config = check_permissions = True

    console.print(Panel.fit(
        "[bold blue]系统诊断工具[/bold blue]\n"
        "[dim]检查系统配置和依赖状态[/dim]",
        border_style="blue"
    ))

    issues = []

    if check_deps:
        console.print("\n🔍 检查依赖库...")
        deps_issues = _check_dependencies()
        issues.extend(deps_issues)

    if check_config:
        console.print("\n⚙️ 检查配置文件...")
        config_issues = _check_configuration()
        issues.extend(config_issues)

    if check_permissions:
        console.print("\n🔐 检查文件权限...")
        perm_issues = _check_permissions()
        issues.extend(perm_issues)

    # 显示诊断结果
    if issues:
        console.print(f"\n❌ 发现 {len(issues)} 个问题:")
        for issue in issues:
            console.print(f"  • {issue}")
    else:
        console.print("\n✅ 系统状态良好，未发现问题")


def _check_dependencies():
    """检查依赖库"""
    issues = []
    required_deps = {
        'click': '命令行界面',
        'rich': '终端美化',
        'plotly': '可视化图表',
        'fastapi': 'Web API框架',
        'networkx': '网络图分析'
    }

    for dep, desc in required_deps.items():
        try:
            __import__(dep)
            console.print(f"  ✅ {dep} - {desc}")
        except ImportError:
            issue = f"{dep} 未安装 - {desc}"
            issues.append(issue)
            console.print(f"  ❌ {issue}")

    return issues


def _check_configuration():
    """检查配置文件"""
    issues = []
    config_file = Path.home() / '.netanalysis' / 'config.json'

    if config_file.exists():
        console.print(f"  ✅ 配置文件存在: {config_file}")
        try:
            import json
            with open(config_file, 'r') as f:
                config = json.load(f)
            console.print(f"  ✅ 配置文件格式正确，包含 {len(config)} 个配置项")
        except Exception as e:
            issue = f"配置文件格式错误: {e}"
            issues.append(issue)
            console.print(f"  ❌ {issue}")
    else:
        console.print(f"  ⚠️ 配置文件不存在: {config_file}")
        console.print("    将使用默认配置")

    return issues


def _check_permissions():
    """检查文件权限"""
    issues = []

    # 检查当前目录写权限
    try:
        test_file = Path("test_write_permission.tmp")
        test_file.write_text("test")
        test_file.unlink()
        console.print("  ✅ 当前目录具有写权限")
    except Exception as e:
        issue = f"当前目录无写权限: {e}"
        issues.append(issue)
        console.print(f"  ❌ {issue}")

    # 检查上传目录
    upload_dir = Path("uploads")
    if upload_dir.exists():
        if upload_dir.is_dir():
            console.print(f"  ✅ 上传目录存在: {upload_dir}")
        else:
            issue = f"上传路径不是目录: {upload_dir}"
            issues.append(issue)
            console.print(f"  ❌ {issue}")
    else:
        console.print(f"  ⚠️ 上传目录不存在，将自动创建: {upload_dir}")

    return issues


def create_cli() -> click.Group:
    """
    创建CLI应用
    
    Returns:
        click.Group: CLI应用对象
    """
    return cli


def main():
    """CLI主入口函数"""
    if not CLI_AVAILABLE:
        print("错误: 缺少必要的依赖库")
        print("请安装: pip install click rich")
        sys.exit(1)
    
    cli()


if __name__ == '__main__':
    main()
