#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PCAP格式解析器
PCAP Format Parser

实现对PCAP格式文件的解析，支持：
- 标准PCAP格式（libpcap）
- 大小端字节序自动检测
- 流式解析支持大文件
- 完整的协议栈解析

使用Scapy库进行底层数据包解析，提供高精度的协议识别。
"""

import struct
import logging
from typing import Iterator, Optional, Dict, Any
from datetime import datetime
from pathlib import Path

try:
    from scapy.all import rdpcap, PcapReader, Ether, IP, IPv6, TCP, UDP, ICMP
    from scapy.packet import Packet as ScapyPacket
    SCAPY_AVAILABLE = True
except ImportError:
    SCAPY_AVAILABLE = False
    logging.warning("Scapy未安装，PCAP解析功能将受限")

from ..core.parser import PacketParser
from ..core.models import Packet, FileMetadata, ProtocolType, PacketDirection
from ..core.exceptions import ParseError, FileFormatError, CorruptedFileError
from ..core.utils import parse_timestamp, validate_ip_address, normalize_mac_address

logger = logging.getLogger(__name__)


class PcapParser(PacketParser):
    """
    PCAP格式解析器
    
    支持解析标准的PCAP格式文件，使用Scapy库进行数据包解析。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化PCAP解析器
        
        Args:
            config: 解析器配置参数
        """
        super().__init__(config)
        
        # 支持的文件扩展名
        self.supported_extensions = ['pcap', 'cap', 'dmp']
        
        # 检查Scapy可用性
        if not SCAPY_AVAILABLE:
            raise ImportError("PCAP解析器需要安装Scapy库: pip install scapy")
        
        # 解析选项
        self.use_scapy_reader = self.config.get("use_scapy_reader", True)
        self.extract_payload = self.config.get("extract_payload", False)
        self.max_payload_size = self.config.get("max_payload_size", 1024)
    
    def can_parse(self, file_path: str) -> bool:
        """
        检查是否可以解析指定的PCAP文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否支持解析该文件
        """
        try:
            # 检查文件扩展名
            path = Path(file_path)
            if path.suffix.lower().lstrip('.') not in self.supported_extensions:
                return False
            
            # 检查PCAP文件头魔数
            with open(file_path, 'rb') as f:
                header = f.read(4)
                if len(header) < 4:
                    return False
                
                magic = struct.unpack('<I', header)[0]
                # PCAP魔数：0xa1b2c3d4 (little-endian) 或 0xd4c3b2a1 (big-endian)
                return magic in (0xa1b2c3d4, 0xd4c3b2a1)
        
        except Exception as e:
            logger.debug(f"PCAP格式检测失败: {e}")
            return False
    
    def parse_file(self, file_path: str) -> Iterator[Packet]:
        """
        解析PCAP文件并返回数据包迭代器
        
        Args:
            file_path: PCAP文件路径
            
        Yields:
            Packet: 解析出的数据包对象
            
        Raises:
            ParseError: 解析错误
            FileFormatError: 文件格式错误
            CorruptedFileError: 文件损坏
        """
        if not self.can_parse(file_path):
            raise FileFormatError(f"不支持的PCAP文件格式: {file_path}")
        
        self._start_parsing()
        
        try:
            if self.use_scapy_reader:
                # 使用Scapy的PcapReader进行流式读取
                yield from self._parse_with_scapy_reader(file_path)
            else:
                # 使用rdpcap一次性读取（适用于小文件）
                yield from self._parse_with_rdpcap(file_path)
        
        except Exception as e:
            logger.error(f"PCAP文件解析失败: {e}")
            raise ParseError(f"PCAP解析错误: {e}", file_path=file_path, cause=e)
        
        finally:
            self._end_parsing()
    
    def _parse_with_scapy_reader(self, file_path: str) -> Iterator[Packet]:
        """
        使用Scapy PcapReader进行流式解析
        
        Args:
            file_path: PCAP文件路径
            
        Yields:
            Packet: 解析出的数据包对象
        """
        try:
            with PcapReader(file_path) as pcap_reader:
                packet_index = 0
                
                for scapy_packet in pcap_reader:
                    try:
                        packet = self._convert_scapy_packet(scapy_packet, packet_index)
                        if packet:
                            yield packet
                            self.stats["total_packets"] += 1
                            packet_index += 1
                        
                        # 检查是否达到最大数据包限制
                        if self.max_packets and packet_index >= self.max_packets:
                            break
                    
                    except Exception as e:
                        logger.warning(f"跳过损坏的数据包 #{packet_index}: {e}")
                        self.stats["failed_packets"] += 1
                        continue
        
        except Exception as e:
            raise CorruptedFileError(f"PCAP文件读取失败: {e}", file_path=file_path, cause=e)
    
    def _parse_with_rdpcap(self, file_path: str) -> Iterator[Packet]:
        """
        使用rdpcap一次性读取解析
        
        Args:
            file_path: PCAP文件路径
            
        Yields:
            Packet: 解析出的数据包对象
        """
        try:
            packets = rdpcap(file_path)
            self.stats["total_packets"] = len(packets)
            
            for packet_index, scapy_packet in enumerate(packets):
                try:
                    packet = self._convert_scapy_packet(scapy_packet, packet_index)
                    if packet:
                        yield packet
                    
                    # 检查是否达到最大数据包限制
                    if self.max_packets and packet_index >= self.max_packets:
                        break
                
                except Exception as e:
                    logger.warning(f"跳过损坏的数据包 #{packet_index}: {e}")
                    self.stats["failed_packets"] += 1
                    continue
        
        except Exception as e:
            raise CorruptedFileError(f"PCAP文件读取失败: {e}", file_path=file_path, cause=e)
    
    def _convert_scapy_packet(self, scapy_packet: ScapyPacket, index: int) -> Optional[Packet]:
        """
        将Scapy数据包转换为内部Packet对象
        
        Args:
            scapy_packet: Scapy数据包对象
            index: 数据包索引
            
        Returns:
            Optional[Packet]: 转换后的数据包对象
        """
        try:
            # 提取时间戳
            timestamp = datetime.fromtimestamp(float(scapy_packet.time))
            
            # 提取基础信息
            packet_size = len(scapy_packet)
            
            # 初始化数据包对象
            packet = Packet(
                timestamp=timestamp,
                size=packet_size,
            )
            
            # 解析以太网层
            if scapy_packet.haslayer(Ether):
                ether = scapy_packet[Ether]
                packet.src_mac = normalize_mac_address(ether.src)
                packet.dst_mac = normalize_mac_address(ether.dst)
            
            # 解析IP层
            if scapy_packet.haslayer(IP):
                ip = scapy_packet[IP]
                packet.src_ip = ip.src
                packet.dst_ip = ip.dst
                packet.ip_version = 4
                packet.ttl = ip.ttl
                packet.headers['ip'] = {
                    'version': ip.version,
                    'ihl': ip.ihl,
                    'tos': ip.tos,
                    'len': ip.len,
                    'id': ip.id,
                    'flags': ip.flags,
                    'frag': ip.frag,
                    'ttl': ip.ttl,
                    'proto': ip.proto,
                    'chksum': ip.chksum,
                }
            
            elif scapy_packet.haslayer(IPv6):
                ipv6 = scapy_packet[IPv6]
                packet.src_ip = ipv6.src
                packet.dst_ip = ipv6.dst
                packet.ip_version = 6
                packet.ttl = ipv6.hlim
                packet.headers['ipv6'] = {
                    'version': ipv6.version,
                    'tc': ipv6.tc,
                    'fl': ipv6.fl,
                    'plen': ipv6.plen,
                    'nh': ipv6.nh,
                    'hlim': ipv6.hlim,
                }
            
            # 解析传输层
            if scapy_packet.haslayer(TCP):
                tcp = scapy_packet[TCP]
                packet.src_port = tcp.sport
                packet.dst_port = tcp.dport
                packet.protocol = ProtocolType.TCP
                packet.flags = {
                    'fin': bool(tcp.flags & 0x01),
                    'syn': bool(tcp.flags & 0x02),
                    'rst': bool(tcp.flags & 0x04),
                    'psh': bool(tcp.flags & 0x08),
                    'ack': bool(tcp.flags & 0x10),
                    'urg': bool(tcp.flags & 0x20),
                }
                packet.headers['tcp'] = {
                    'sport': tcp.sport,
                    'dport': tcp.dport,
                    'seq': tcp.seq,
                    'ack': tcp.ack,
                    'dataofs': tcp.dataofs,
                    'reserved': tcp.reserved,
                    'flags': tcp.flags,
                    'window': tcp.window,
                    'chksum': tcp.chksum,
                    'urgptr': tcp.urgptr,
                }
            
            elif scapy_packet.haslayer(UDP):
                udp = scapy_packet[UDP]
                packet.src_port = udp.sport
                packet.dst_port = udp.dport
                packet.protocol = ProtocolType.UDP
                packet.headers['udp'] = {
                    'sport': udp.sport,
                    'dport': udp.dport,
                    'len': udp.len,
                    'chksum': udp.chksum,
                }
            
            elif scapy_packet.haslayer(ICMP):
                icmp = scapy_packet[ICMP]
                packet.protocol = ProtocolType.ICMP
                packet.headers['icmp'] = {
                    'type': icmp.type,
                    'code': icmp.code,
                    'chksum': icmp.chksum,
                    'id': icmp.id,
                    'seq': icmp.seq,
                }
            
            # 提取载荷
            if self.extract_payload and scapy_packet.payload:
                payload = bytes(scapy_packet.payload)
                if len(payload) <= self.max_payload_size:
                    packet.payload = payload
                    packet.payload_size = len(payload)
                else:
                    packet.payload_size = len(payload)
            
            # 确定数据包方向（简单实现）
            packet.direction = self._determine_packet_direction(packet)
            
            return packet
        
        except Exception as e:
            logger.warning(f"数据包转换失败 #{index}: {e}")
            return None
    
    def _determine_packet_direction(self, packet: Packet) -> PacketDirection:
        """
        确定数据包方向
        
        Args:
            packet: 数据包对象
            
        Returns:
            PacketDirection: 数据包方向
        """
        if not packet.src_ip or not packet.dst_ip:
            return PacketDirection.UNKNOWN
        
        try:
            from ..core.utils import is_private_ip
            
            src_private = is_private_ip(packet.src_ip)
            dst_private = is_private_ip(packet.dst_ip)
            
            if src_private and not dst_private:
                return PacketDirection.OUTBOUND
            elif not src_private and dst_private:
                return PacketDirection.INBOUND
            elif src_private and dst_private:
                return PacketDirection.INTERNAL
            else:
                return PacketDirection.UNKNOWN
        
        except Exception:
            return PacketDirection.UNKNOWN
    
    def get_file_metadata(self, file_path: str) -> FileMetadata:
        """
        获取PCAP文件元数据信息
        
        Args:
            file_path: PCAP文件路径
            
        Returns:
            FileMetadata: 文件元数据对象
        """
        metadata = self._create_file_metadata(file_path)
        
        try:
            # 读取PCAP文件头信息
            with open(file_path, 'rb') as f:
                # 读取全局头部（24字节）
                global_header = f.read(24)
                if len(global_header) < 24:
                    raise CorruptedFileError("PCAP文件头不完整")
                
                # 解析魔数和字节序
                magic = struct.unpack('<I', global_header[:4])[0]
                if magic == 0xa1b2c3d4:
                    endian = '<'  # little-endian
                elif magic == 0xd4c3b2a1:
                    endian = '>'  # big-endian
                else:
                    raise FileFormatError("无效的PCAP魔数")
                
                # 解析全局头部
                header_format = f'{endian}IHHIIII'
                (magic, version_major, version_minor, thiszone, 
                 sigfigs, snaplen, network) = struct.unpack(header_format, global_header)
                
                # 快速统计数据包数量
                packet_count = 0
                min_timestamp = None
                max_timestamp = None
                
                while True:
                    # 读取数据包头部（16字节）
                    packet_header = f.read(16)
                    if len(packet_header) < 16:
                        break
                    
                    # 解析数据包头部
                    (ts_sec, ts_usec, incl_len, orig_len) = struct.unpack(
                        f'{endian}IIII', packet_header
                    )
                    
                    # 更新统计信息
                    packet_count += 1
                    timestamp = ts_sec + ts_usec / 1000000.0
                    
                    if min_timestamp is None or timestamp < min_timestamp:
                        min_timestamp = timestamp
                    if max_timestamp is None or timestamp > max_timestamp:
                        max_timestamp = timestamp
                    
                    # 跳过数据包数据
                    f.seek(incl_len, 1)
                
                # 更新元数据
                metadata.total_packets = packet_count
                if min_timestamp and max_timestamp:
                    metadata.time_range = (
                        datetime.fromtimestamp(min_timestamp),
                        datetime.fromtimestamp(max_timestamp)
                    )
                
                # 添加PCAP特定信息
                metadata.comments.append(f"PCAP版本: {version_major}.{version_minor}")
                metadata.comments.append(f"网络类型: {network}")
                metadata.comments.append(f"快照长度: {snaplen}")
        
        except Exception as e:
            logger.warning(f"获取PCAP元数据失败: {e}")
        
        return metadata
