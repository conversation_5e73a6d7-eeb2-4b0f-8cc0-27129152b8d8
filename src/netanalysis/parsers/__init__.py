#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络数据包解析器模块
Network Packet Parsers Module

包含各种网络数据包格式的解析器实现：
- PCAP格式解析器
- PCAPNG格式解析器
- tcpdump输出解析器
- tshark输出解析器

所有解析器都继承自PacketParser基类，提供统一的接口。
"""

from .pcap_parser import PcapParser
from .pcapng_parser import PcapngParser
from .tcpdump_parser import TcpdumpParser
from .tshark_parser import TsharkParser

# 导出所有解析器类
__all__ = [
    "PcapParser",
    "PcapngParser", 
    "TcpdumpParser",
    "TsharkParser",
]

# 自动注册所有解析器
def register_all_parsers():
    """注册所有可用的解析器到全局注册表"""
    from ..core.parser import parser_registry
    
    # 注册PCAP解析器
    parser_registry.register("pcap", PcapParser)
    
    # 注册PCAPNG解析器
    parser_registry.register("pcapng", PcapngParser)
    
    # 注册tcpdump解析器
    parser_registry.register("tcpdump", TcpdumpParser)
    
    # 注册tshark解析器
    parser_registry.register("tshark", TsharkParser)

# 模块导入时自动注册
register_all_parsers()
