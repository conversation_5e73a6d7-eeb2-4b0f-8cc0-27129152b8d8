#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Claude API客户端实现
Claude API Client Implementation

实现Anthropic Claude API的集成，提供网络分析的AI增强功能。
支持Claude-3等模型，提供智能的网络分析和建议。
"""

import logging
import json
from typing import Dict, List, Optional, Any
import time

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False
    logging.warning("Anthropic库未安装，Claude客户端功能将受限")

from .llm_client import LLMClient, LLMMessage, LLMResponse, LLMProvider, LLMClientError, MessageRole


class ClaudeClient(LLMClient):
    """
    Claude API客户端
    
    集成Anthropic Claude API，提供网络分析的AI增强功能。
    支持Claude-3等模型，提供智能的网络分析和建议。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Claude客户端
        
        Args:
            config: 配置参数，包括：
                - api_key: Anthropic API密钥
                - model: 模型名称（默认claude-3-sonnet-20240229）
                - temperature: 温度参数（默认0.7）
                - max_tokens: 最大令牌数（默认1000）
                - timeout: 请求超时时间（默认30秒）
        """
        super().__init__(config)
        
        # 设置默认参数
        self.api_key = config.get('api_key', '')
        self.model = config.get('model', 'claude-3-sonnet-20240229')
        self.temperature = config.get('temperature', 0.7)
        self.max_tokens = config.get('max_tokens', 1000)
        self.timeout = config.get('timeout', 30)
        
        # 初始化Claude客户端
        if ANTHROPIC_AVAILABLE and self.api_key:
            self.client = anthropic.Anthropic(api_key=self.api_key)
            self.logger.info(f"Claude客户端初始化成功，模型: {self.model}")
        else:
            self.logger.warning("Claude客户端初始化失败：缺少API密钥或Anthropic库")
            self.client = None
    
    def _get_provider(self) -> LLMProvider:
        """获取提供商类型"""
        return LLMProvider.CLAUDE
    
    def _validate_config(self) -> bool:
        """
        验证配置参数
        
        Returns:
            bool: 配置是否有效
        """
        if not ANTHROPIC_AVAILABLE:
            self.logger.error("Anthropic库未安装")
            return False
        
        if not self.api_key:
            self.logger.error("Anthropic API密钥未配置")
            return False
        
        if not self.client:
            self.logger.error("Claude客户端未初始化")
            return False
        
        if not self.model.startswith('claude-'):
            self.logger.warning(f"未知的模型: {self.model}")
        
        return True
    
    def _convert_messages(self, messages: List[LLMMessage]) -> List[Dict[str, str]]:
        """
        转换消息格式为Claude API格式
        
        Args:
            messages: LLM消息列表
            
        Returns:
            List[Dict[str, str]]: Claude API消息格式
        """
        claude_messages = []
        system_message = None
        
        for msg in messages:
            if msg.role == MessageRole.SYSTEM:
                # Claude API将系统消息单独处理
                system_message = msg.content
            else:
                claude_messages.append({
                    "role": msg.role.value,
                    "content": msg.content
                })
        
        return claude_messages, system_message
    
    def _make_request(self, messages: List[LLMMessage], **kwargs) -> LLMResponse:
        """
        发送请求到Claude API
        
        Args:
            messages: 消息列表
            **kwargs: 额外参数
            
        Returns:
            LLMResponse: API响应结果
        """
        try:
            # 转换消息格式
            claude_messages, system_message = self._convert_messages(messages)
            
            # 准备请求参数
            request_params = {
                "model": self.model,
                "messages": claude_messages,
                "temperature": kwargs.get('temperature', self.temperature),
                "max_tokens": kwargs.get('max_tokens', self.max_tokens),
            }
            
            # 添加系统消息（如果存在）
            if system_message:
                request_params["system"] = system_message
            
            # 记录请求开始时间
            start_time = time.time()
            
            # 发送请求
            self.logger.info(f"发送Claude API请求: {self.model}")
            response = self.client.messages.create(**request_params)
            
            # 计算请求耗时
            elapsed_time = time.time() - start_time
            
            # 解析响应
            content = response.content[0].text if response.content else ""
            usage = {
                "input_tokens": response.usage.input_tokens,
                "output_tokens": response.usage.output_tokens
            }
            
            self.logger.info(f"Claude API请求成功，耗时: {elapsed_time:.2f}秒")
            
            return LLMResponse(
                content=content,
                provider=self.provider,
                model=self.model,
                usage=usage,
                metadata={
                    "response_id": response.id,
                    "model": response.model,
                    "elapsed_time": elapsed_time,
                    "stop_reason": response.stop_reason
                }
            )
            
        except Exception as e:
            error_msg = f"Claude API请求失败: {str(e)}"
            self.logger.error(error_msg)
            
            return LLMResponse(
                content="",
                provider=self.provider,
                model=self.model,
                success=False,
                error_message=error_msg
            )
    
    def analyze_network_data(self, analysis_data: Dict[str, Any], 
                           analysis_type: str = "comprehensive") -> str:
        """
        分析网络数据并提供AI建议
        
        Args:
            analysis_data: 网络分析数据
            analysis_type: 分析类型（comprehensive, security, performance）
            
        Returns:
            str: AI分析结果和建议
        """
        # 构建系统提示词
        system_prompt = self._get_network_analysis_system_prompt(analysis_type)
        
        # 构建用户提示词
        user_prompt = self._format_analysis_data(analysis_data, analysis_type)
        
        # 发送请求
        response = self.simple_chat(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.3,  # 降低温度以获得更一致的分析结果
            max_tokens=1500
        )
        
        return response
    
    def _get_network_analysis_system_prompt(self, analysis_type: str) -> str:
        """
        获取网络分析系统提示词
        
        Args:
            analysis_type: 分析类型
            
        Returns:
            str: 系统提示词
        """
        base_prompt = """你是一位专业的网络安全和性能分析专家，具有丰富的网络流量分析经验。
你的任务是分析网络数据包统计信息，识别潜在问题，并提供专业的建议。

请用中文回答，并按照以下格式组织你的分析：
1. 🔍 **关键发现**：总结最重要的发现
2. 📊 **数据解读**：解释关键指标的含义
3. ⚠️ **风险评估**：识别潜在的安全风险或性能问题
4. 💡 **优化建议**：提供具体的改进建议
5. 🎯 **行动计划**：建议的下一步行动

请保持专业、准确、实用的分析风格。"""
        
        if analysis_type == "security":
            return base_prompt + "\n\n特别关注安全相关的指标和威胁，重点分析异常流量、攻击模式和安全风险。"
        elif analysis_type == "performance":
            return base_prompt + "\n\n特别关注性能相关的指标，重点分析带宽使用、连接效率和网络优化机会。"
        else:
            return base_prompt + "\n\n提供全面的网络分析，涵盖安全、性能和运维各个方面。"
    
    def _format_analysis_data(self, analysis_data: Dict[str, Any], 
                            analysis_type: str) -> str:
        """
        格式化分析数据为提示词
        
        Args:
            analysis_data: 分析数据
            analysis_type: 分析类型
            
        Returns:
            str: 格式化的提示词
        """
        prompt = f"请分析以下网络数据包统计信息（分析类型：{analysis_type}）：\n\n"
        
        # 添加协议统计信息
        if 'protocol_stats' in analysis_data:
            stats = analysis_data['protocol_stats']
            prompt += "📊 **协议统计信息**：\n"
            prompt += f"- 总数据包数：{stats.get('total_packets', 0)}\n"
            prompt += f"- 总字节数：{stats.get('total_bytes', 0)}\n"
            prompt += f"- 协议分布：{stats.get('protocol_counts', {})}\n"
            prompt += f"- 协议百分比：{stats.get('protocol_percentages', {})}\n\n"
        
        # 添加流量统计信息
        if 'traffic_stats' in analysis_data:
            stats = analysis_data['traffic_stats']
            prompt += "🌊 **流量统计信息**：\n"
            prompt += f"- 包速率：{stats.get('packets_per_second', 0):.2f} 包/秒\n"
            prompt += f"- 字节速率：{stats.get('bytes_per_second', 0):.2f} 字节/秒\n"
            prompt += f"- 比特率：{stats.get('bits_per_second', 0):.2f} bps\n"
            prompt += f"- 平均包大小：{stats.get('average_packet_size', 0):.1f} 字节\n\n"
        
        # 添加异常检测信息
        if 'anomalies' in analysis_data:
            anomalies = analysis_data['anomalies']
            prompt += f"🚨 **异常检测结果**：\n"
            prompt += f"- 检测到异常数量：{len(anomalies)}\n"
            for anomaly in anomalies[:3]:  # 只显示前3个异常
                prompt += f"- {anomaly.get('title', '未知异常')}：{anomaly.get('description', '')}\n"
            prompt += "\n"
        
        # 添加安全威胁信息
        if 'security_threats' in analysis_data:
            threats = analysis_data['security_threats']
            prompt += f"🛡️ **安全威胁检测结果**：\n"
            prompt += f"- 检测到威胁数量：{len(threats)}\n"
            for threat in threats[:3]:  # 只显示前3个威胁
                prompt += f"- {threat.get('title', '未知威胁')}：{threat.get('description', '')}\n"
            prompt += "\n"
        
        prompt += "请基于以上数据提供专业的网络分析和建议。"
        
        return prompt
    
    def get_available_models(self) -> List[str]:
        """
        获取可用的模型列表
        
        Returns:
            List[str]: 可用模型列表
        """
        return [
            'claude-3-opus-20240229',
            'claude-3-sonnet-20240229',
            'claude-3-haiku-20240307'
        ]
