#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络分析提示词模板
Network Analysis Prompt Templates

设计专门的网络分析提示词模板，优化AI分析效果。
包含不同类型的分析模板，如安全分析、性能分析、综合分析等。
"""

from typing import Dict, Any, List
from enum import Enum


class AnalysisType(str, Enum):
    """分析类型枚举"""
    COMPREHENSIVE = "comprehensive"  # 综合分析
    SECURITY = "security"           # 安全分析
    PERFORMANCE = "performance"     # 性能分析
    PROTOCOL = "protocol"          # 协议分析
    ANOMALY = "anomaly"            # 异常分析
    THREAT = "threat"              # 威胁分析


class NetworkAnalysisPrompts:
    """网络分析提示词模板类"""
    
    @staticmethod
    def get_system_prompt(analysis_type: AnalysisType) -> str:
        """
        获取系统提示词
        
        Args:
            analysis_type: 分析类型
            
        Returns:
            str: 系统提示词
        """
        base_prompt = """你是一位资深的网络安全和性能分析专家，拥有超过15年的网络运维和安全分析经验。
你精通各种网络协议、安全威胁识别、性能优化和故障诊断。

你的分析特点：
- 准确识别网络异常和安全威胁
- 提供实用的优化建议和解决方案
- 使用专业术语但保持易懂的表达
- 基于数据事实进行客观分析
- 考虑业务影响和实施可行性

请用中文回答，并按照以下结构组织你的分析：
1. 🔍 **执行摘要**：3-5句话总结最重要的发现
2. 📊 **关键指标解读**：解释重要的网络指标含义
3. ⚠️ **风险与问题识别**：识别潜在风险和现存问题
4. 💡 **优化建议**：提供具体可行的改进措施
5. 🎯 **优先级行动计划**：按重要性排序的行动建议
6. 📈 **预期效果**：说明实施建议后的预期改善"""
        
        type_specific_prompts = {
            AnalysisType.COMPREHENSIVE: """
你需要提供全面的网络分析，涵盖安全、性能、协议使用等各个维度。
重点关注：整体网络健康状况、主要风险点、优化机会。""",
            
            AnalysisType.SECURITY: """
你需要专注于网络安全分析，识别潜在的安全威胁和漏洞。
重点关注：攻击模式、异常行为、安全风险、防护建议。
特别注意DDoS攻击、端口扫描、恶意流量、数据泄露风险。""",
            
            AnalysisType.PERFORMANCE: """
你需要专注于网络性能分析，识别性能瓶颈和优化机会。
重点关注：带宽利用率、延迟、吞吐量、连接效率、资源使用。
特别注意网络拥塞、连接超时、传输效率、负载均衡。""",
            
            AnalysisType.PROTOCOL: """
你需要专注于网络协议分析，评估协议使用的合理性和安全性。
重点关注：协议分布、版本兼容性、加密使用、协议安全性。
特别注意过时协议、未加密通信、协议滥用、配置错误。""",
            
            AnalysisType.ANOMALY: """
你需要专注于异常检测分析，识别不正常的网络行为模式。
重点关注：流量异常、行为偏差、模式变化、统计异常。
特别注意突发流量、异常连接、时间模式异常、地理异常。""",
            
            AnalysisType.THREAT: """
你需要专注于威胁检测分析，识别和评估安全威胁。
重点关注：攻击指标、威胁等级、影响范围、应对措施。
特别注意APT攻击、内部威胁、零日攻击、供应链攻击。"""
        }
        
        return base_prompt + type_specific_prompts.get(analysis_type, "")
    
    @staticmethod
    def format_protocol_stats(stats: Dict[str, Any]) -> str:
        """
        格式化协议统计数据
        
        Args:
            stats: 协议统计数据
            
        Returns:
            str: 格式化的协议统计信息
        """
        if not stats:
            return "无协议统计数据"
        
        text = "📊 **协议统计信息**：\n"
        text += f"• 总数据包：{stats.get('total_packets', 0):,} 个\n"
        text += f"• 总数据量：{stats.get('total_bytes', 0):,} 字节 ({stats.get('total_bytes', 0)/1024/1024:.2f} MB)\n"
        text += f"• 分析时长：{stats.get('duration_seconds', 0):.1f} 秒\n"
        text += f"• 唯一流数：{stats.get('unique_flows', 0):,} 个\n\n"
        
        # 协议分布
        protocol_counts = stats.get('protocol_counts', {})
        if protocol_counts:
            text += "**协议分布**：\n"
            for protocol, count in sorted(protocol_counts.items(), key=lambda x: x[1], reverse=True):
                percentage = stats.get('protocol_percentages', {}).get(protocol, 0)
                text += f"• {protocol.upper()}：{count:,} 包 ({percentage:.1f}%)\n"
            text += "\n"
        
        # Top IP地址
        top_src_ips = stats.get('top_src_ips', [])[:5]
        if top_src_ips:
            text += "**Top源IP地址**：\n"
            for ip, count in top_src_ips:
                text += f"• {ip}：{count:,} 包\n"
            text += "\n"
        
        # Top端口
        top_dst_ports = stats.get('top_dst_ports', [])[:5]
        if top_dst_ports:
            text += "**Top目标端口**：\n"
            for port, count in top_dst_ports:
                text += f"• 端口 {port}：{count:,} 包\n"
            text += "\n"
        
        return text
    
    @staticmethod
    def format_traffic_stats(stats: Dict[str, Any]) -> str:
        """
        格式化流量统计数据
        
        Args:
            stats: 流量统计数据
            
        Returns:
            str: 格式化的流量统计信息
        """
        if not stats:
            return "无流量统计数据"
        
        text = "🌊 **流量统计信息**：\n"
        
        # 基础速率
        pps = stats.get('packets_per_second', 0)
        bps = stats.get('bits_per_second', 0)
        avg_size = stats.get('average_packet_size', 0)
        
        text += f"• 包速率：{pps:.2f} 包/秒\n"
        text += f"• 比特率：{bps:,.0f} bps ({bps/1000000:.2f} Mbps)\n"
        text += f"• 平均包大小：{avg_size:.1f} 字节\n"
        
        # 连接统计
        total_conn = stats.get('total_connections', 0)
        active_conn = stats.get('active_connections', 0)
        
        text += f"• 总连接数：{total_conn:,} 个\n"
        text += f"• 活跃连接：{active_conn:,} 个\n\n"
        
        # 性能评估
        if pps > 10000:
            text += "⚡ **流量等级**：高流量（需要关注性能）\n"
        elif pps > 1000:
            text += "📈 **流量等级**：中等流量（正常范围）\n"
        else:
            text += "📉 **流量等级**：低流量（可能是测试环境）\n"
        
        return text
    
    @staticmethod
    def format_security_analysis(anomalies: List[Dict], threats: List[Dict]) -> str:
        """
        格式化安全分析数据
        
        Args:
            anomalies: 异常列表
            threats: 威胁列表
            
        Returns:
            str: 格式化的安全分析信息
        """
        text = "🛡️ **安全分析结果**：\n"
        
        # 异常检测结果
        text += f"• 检测到异常：{len(anomalies)} 个\n"
        if anomalies:
            text += "**异常详情**：\n"
            for i, anomaly in enumerate(anomalies[:3], 1):
                title = anomaly.get('title', '未知异常')
                desc = anomaly.get('description', '')
                severity = anomaly.get('severity', 'unknown')
                text += f"  {i}. [{severity.upper()}] {title}\n"
                text += f"     {desc}\n"
            if len(anomalies) > 3:
                text += f"     ... 还有 {len(anomalies) - 3} 个异常\n"
            text += "\n"
        
        # 威胁检测结果
        text += f"• 检测到威胁：{len(threats)} 个\n"
        if threats:
            text += "**威胁详情**：\n"
            for i, threat in enumerate(threats[:3], 1):
                title = threat.get('title', '未知威胁')
                desc = threat.get('description', '')
                threat_type = threat.get('threat_type', 'unknown')
                severity = threat.get('severity', 'unknown')
                text += f"  {i}. [{severity.upper()}] {threat_type.upper()}: {title}\n"
                text += f"     {desc}\n"
            if len(threats) > 3:
                text += f"     ... 还有 {len(threats) - 3} 个威胁\n"
            text += "\n"
        
        # 安全评级
        total_issues = len(anomalies) + len(threats)
        if total_issues == 0:
            text += "✅ **安全评级**：良好（未发现明显安全问题）\n"
        elif total_issues <= 2:
            text += "⚠️ **安全评级**：注意（发现少量安全问题）\n"
        elif total_issues <= 5:
            text += "🚨 **安全评级**：警告（发现多个安全问题）\n"
        else:
            text += "🔴 **安全评级**：严重（发现大量安全问题）\n"
        
        return text
    
    @staticmethod
    def format_application_analysis(app_analysis: Dict[str, Any]) -> str:
        """
        格式化应用层分析数据
        
        Args:
            app_analysis: 应用层分析数据
            
        Returns:
            str: 格式化的应用层分析信息
        """
        if not app_analysis:
            return "无应用层分析数据"
        
        text = "🔍 **应用层协议分析**：\n"
        
        # 协议分布
        protocol_dist = app_analysis.get('protocol_distribution', {})
        if protocol_dist:
            text += "**识别的应用协议**：\n"
            for protocol, count in sorted(protocol_dist.items(), key=lambda x: x[1], reverse=True):
                text += f"• {protocol.upper()}：{count:,} 个连接\n"
            text += "\n"
        
        # HTTP分析
        http_analysis = app_analysis.get('http_analysis', {})
        if http_analysis and (http_analysis.get('requests', 0) > 0 or http_analysis.get('responses', 0) > 0):
            text += "**HTTP流量分析**：\n"
            text += f"• HTTP请求：{http_analysis.get('requests', 0):,} 个\n"
            text += f"• HTTP响应：{http_analysis.get('responses', 0):,} 个\n"
            text += "\n"
        
        # DNS分析
        dns_analysis = app_analysis.get('dns_analysis', {})
        if dns_analysis and (dns_analysis.get('queries', 0) > 0 or dns_analysis.get('responses', 0) > 0):
            text += "**DNS流量分析**：\n"
            text += f"• DNS查询：{dns_analysis.get('queries', 0):,} 个\n"
            text += f"• DNS响应：{dns_analysis.get('responses', 0):,} 个\n"
            text += "\n"
        
        return text
    
    @staticmethod
    def create_analysis_prompt(analysis_type: AnalysisType, data: Dict[str, Any]) -> str:
        """
        创建完整的分析提示词
        
        Args:
            analysis_type: 分析类型
            data: 分析数据
            
        Returns:
            str: 完整的分析提示词
        """
        prompt = f"请对以下网络数据进行{analysis_type.value}分析：\n\n"
        
        # 添加协议统计
        if 'protocol_stats' in data:
            prompt += NetworkAnalysisPrompts.format_protocol_stats(data['protocol_stats'])
        
        # 添加流量统计
        if 'traffic_stats' in data:
            prompt += NetworkAnalysisPrompts.format_traffic_stats(data['traffic_stats'])
        
        # 添加安全分析
        anomalies = data.get('anomalies', [])
        threats = data.get('security_threats', [])
        if anomalies or threats:
            prompt += NetworkAnalysisPrompts.format_security_analysis(anomalies, threats)
        
        # 添加应用层分析
        if 'app_analysis' in data:
            prompt += NetworkAnalysisPrompts.format_application_analysis(data['app_analysis'])
        
        # 添加分析要求
        prompt += "\n" + NetworkAnalysisPrompts._get_analysis_requirements(analysis_type)
        
        return prompt
    
    @staticmethod
    def _get_analysis_requirements(analysis_type: AnalysisType) -> str:
        """
        获取特定分析类型的要求
        
        Args:
            analysis_type: 分析类型
            
        Returns:
            str: 分析要求
        """
        requirements = {
            AnalysisType.COMPREHENSIVE: """
请提供全面的网络分析，包括：
- 网络健康状况评估
- 主要风险点识别
- 性能优化建议
- 安全加固建议
- 运维改进建议""",
            
            AnalysisType.SECURITY: """
请重点进行安全分析，包括：
- 威胁等级评估
- 攻击向量分析
- 安全漏洞识别
- 防护措施建议
- 应急响应计划""",
            
            AnalysisType.PERFORMANCE: """
请重点进行性能分析，包括：
- 性能瓶颈识别
- 资源利用率评估
- 优化机会分析
- 容量规划建议
- 监控指标建议""",
            
            AnalysisType.PROTOCOL: """
请重点进行协议分析，包括：
- 协议使用合理性评估
- 安全协议采用情况
- 协议配置优化建议
- 兼容性问题识别
- 标准合规性检查""",
            
            AnalysisType.ANOMALY: """
请重点进行异常分析，包括：
- 异常模式识别
- 根因分析
- 影响范围评估
- 处理优先级排序
- 预防措施建议""",
            
            AnalysisType.THREAT: """
请重点进行威胁分析，包括：
- 威胁类型识别
- 攻击意图推断
- 影响程度评估
- 缓解措施建议
- 取证分析指导"""
        }
        
        return requirements.get(analysis_type, "请提供专业的网络分析和建议。")
