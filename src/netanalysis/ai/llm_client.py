#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大语言模型客户端抽象接口
LLM Client Abstract Interface

提供统一的LLM客户端抽象接口，支持多种AI服务提供商，
包括OpenAI、<PERSON>、本地模型等。
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
import json


class LLMProvider(str, Enum):
    """LLM服务提供商枚举"""
    OPENAI = "openai"
    CLAUDE = "claude"
    LOCAL = "local"
    MOCK = "mock"  # 用于测试


class MessageRole(str, Enum):
    """消息角色枚举"""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"


@dataclass
class LLMMessage:
    """LLM消息数据结构"""
    role: MessageRole
    content: str
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class LLMResponse:
    """LLM响应数据结构"""
    content: str
    provider: LLMProvider
    model: str
    usage: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    success: bool = True
    error_message: Optional[str] = None


class LLMClientError(Exception):
    """LLM客户端异常类"""
    pass


class LLMClient(ABC):
    """
    LLM客户端抽象基类
    
    定义了所有LLM客户端必须实现的接口方法，
    确保不同提供商的客户端具有统一的调用方式。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化LLM客户端
        
        Args:
            config: 客户端配置参数
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.provider = self._get_provider()
        self.model = config.get('model', 'default')
        
    @abstractmethod
    def _get_provider(self) -> LLMProvider:
        """获取提供商类型"""
        pass
    
    @abstractmethod
    def _validate_config(self) -> bool:
        """验证配置参数"""
        pass
    
    @abstractmethod
    def _make_request(self, messages: List[LLMMessage], **kwargs) -> LLMResponse:
        """发送请求到LLM服务"""
        pass
    
    def chat(self, messages: List[LLMMessage], **kwargs) -> LLMResponse:
        """
        发送聊天请求
        
        Args:
            messages: 消息列表
            **kwargs: 额外参数（temperature、max_tokens等）
            
        Returns:
            LLMResponse: LLM响应结果
            
        Raises:
            LLMClientError: 请求失败时抛出异常
        """
        try:
            # 验证配置
            if not self._validate_config():
                raise LLMClientError("LLM客户端配置验证失败")
            
            # 验证消息
            if not messages:
                raise LLMClientError("消息列表不能为空")
            
            # 记录请求日志
            self.logger.info(f"发送LLM请求: {self.provider.value}, 模型: {self.model}, 消息数: {len(messages)}")
            
            # 发送请求
            response = self._make_request(messages, **kwargs)
            
            # 记录响应日志
            if response.success:
                self.logger.info(f"LLM请求成功: 响应长度 {len(response.content)} 字符")
            else:
                self.logger.error(f"LLM请求失败: {response.error_message}")
            
            return response
            
        except Exception as e:
            error_msg = f"LLM请求异常: {str(e)}"
            self.logger.error(error_msg)
            return LLMResponse(
                content="",
                provider=self.provider,
                model=self.model,
                success=False,
                error_message=error_msg
            )
    
    def simple_chat(self, prompt: str, system_prompt: Optional[str] = None, **kwargs) -> str:
        """
        简单聊天接口
        
        Args:
            prompt: 用户提示词
            system_prompt: 系统提示词（可选）
            **kwargs: 额外参数
            
        Returns:
            str: LLM响应内容
        """
        messages = []
        
        # 添加系统提示词
        if system_prompt:
            messages.append(LLMMessage(
                role=MessageRole.SYSTEM,
                content=system_prompt
            ))
        
        # 添加用户提示词
        messages.append(LLMMessage(
            role=MessageRole.USER,
            content=prompt
        ))
        
        response = self.chat(messages, **kwargs)
        return response.content if response.success else f"错误: {response.error_message}"
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            Dict[str, Any]: 模型信息
        """
        return {
            "provider": self.provider.value,
            "model": self.model,
            "config": self.config
        }


class MockLLMClient(LLMClient):
    """
    模拟LLM客户端
    
    用于测试和演示，返回预设的响应内容。
    """
    
    def _get_provider(self) -> LLMProvider:
        return LLMProvider.MOCK
    
    def _validate_config(self) -> bool:
        return True
    
    def _make_request(self, messages: List[LLMMessage], **kwargs) -> LLMResponse:
        """
        模拟LLM请求
        
        根据消息内容返回预设的响应
        """
        # 获取最后一条用户消息
        user_messages = [msg for msg in messages if msg.role == MessageRole.USER]
        if not user_messages:
            return LLMResponse(
                content="没有找到用户消息",
                provider=self.provider,
                model=self.model,
                success=False,
                error_message="没有用户消息"
            )
        
        last_user_message = user_messages[-1].content.lower()
        
        # 根据关键词生成响应
        if "网络分析" in last_user_message or "network analysis" in last_user_message:
            content = """基于您提供的网络数据包分析结果，我发现以下关键信息：

🔍 **流量特征分析**：
- 网络流量以TCP协议为主，表明主要是Web浏览和数据传输活动
- UDP流量主要来自DNS查询，网络解析活动正常
- ICMP流量比例较高，需要关注是否存在网络故障或攻击

🚨 **安全风险评估**：
- 检测到可能的DDoS攻击模式，建议立即采取防护措施
- HTTP流量未加密，建议迁移到HTTPS提高安全性
- 端口使用模式正常，未发现明显的端口扫描行为

💡 **优化建议**：
1. 配置DNS缓存以减少DNS查询频率
2. 实施HTTPS加密保护数据传输安全
3. 监控ICMP流量，排查网络连通性问题
4. 建立流量基线，设置异常告警阈值

📊 **性能评估**：
- 当前网络负载较低，适合测试环境
- 平均包大小合理，传输效率良好
- 连接复用率有待提升，可优化应用配置"""
        
        elif "协议" in last_user_message or "protocol" in last_user_message:
            content = """协议分析显示您的网络环境具有以下特征：

📡 **协议分布合理**：TCP、UDP、ICMP协议比例符合典型企业网络模式
🔒 **安全协议使用**：建议增加HTTPS、SSH等加密协议的使用比例
⚡ **性能优化**：可通过连接池和缓存机制提升网络效率"""
        
        elif "异常" in last_user_message or "anomaly" in last_user_message:
            content = """异常检测分析结果：

✅ **正常流量模式**：大部分流量符合预期的网络行为模式
⚠️ **潜在风险**：发现疑似DDoS攻击特征，建议加强监控
🛡️ **防护建议**：建议部署DDoS防护设备和流量清洗服务"""
        
        else:
            content = """我是网络分析AI助手，专门帮助分析网络数据包和流量模式。

我可以帮您：
- 分析网络协议分布和使用模式
- 识别潜在的安全威胁和异常行为
- 提供网络优化和安全加固建议
- 解读复杂的网络分析报告

请提供您的网络分析数据，我将为您提供专业的分析和建议。"""
        
        return LLMResponse(
            content=content,
            provider=self.provider,
            model=self.model,
            usage={"prompt_tokens": len(last_user_message), "completion_tokens": len(content)},
            metadata={"mock_response": True}
        )


def create_llm_client(provider: str, config: Dict[str, Any]) -> LLMClient:
    """
    LLM客户端工厂函数
    
    Args:
        provider: 提供商名称
        config: 客户端配置
        
    Returns:
        LLMClient: LLM客户端实例
        
    Raises:
        LLMClientError: 不支持的提供商
    """
    provider_enum = LLMProvider(provider.lower())
    
    if provider_enum == LLMProvider.MOCK:
        return MockLLMClient(config)
    elif provider_enum == LLMProvider.OPENAI:
        # 这里将在后续实现OpenAI客户端
        from .openai_client import OpenAIClient
        return OpenAIClient(config)
    elif provider_enum == LLMProvider.CLAUDE:
        from .claude_client import ClaudeClient
        return ClaudeClient(config)
    elif provider_enum == LLMProvider.LOCAL:
        # 这里将在后续实现本地模型客户端
        raise LLMClientError("本地模型客户端尚未实现")
    else:
        raise LLMClientError(f"不支持的LLM提供商: {provider}")
