#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
上下文感知分析器
Context-Aware Analyzer

开发基于上下文的智能分析，提供个性化的分析建议。
根据网络环境、业务场景、历史数据等上下文信息，
提供更精准和个性化的网络分析结果。
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

from .llm_client import LLMClient, create_llm_client, LLMMessage, MessageRole
from .prompt_templates import NetworkAnalysisPrompts, AnalysisType


class NetworkEnvironment(str, Enum):
    """网络环境类型"""
    ENTERPRISE = "enterprise"      # 企业网络
    DATA_CENTER = "data_center"    # 数据中心
    CLOUD = "cloud"               # 云环境
    HOME_OFFICE = "home_office"   # 家庭办公
    IOT = "iot"                   # 物联网
    INDUSTRIAL = "industrial"     # 工业网络
    UNKNOWN = "unknown"           # 未知环境


class BusinessContext(str, Enum):
    """业务上下文类型"""
    FINANCIAL = "financial"       # 金融服务
    HEALTHCARE = "healthcare"     # 医疗健康
    EDUCATION = "education"       # 教育机构
    GOVERNMENT = "government"     # 政府机构
    RETAIL = "retail"            # 零售电商
    MANUFACTURING = "manufacturing" # 制造业
    TECHNOLOGY = "technology"     # 科技公司
    GENERAL = "general"          # 通用业务


@dataclass
class AnalysisContext:
    """分析上下文信息"""
    # 环境信息
    network_environment: NetworkEnvironment = NetworkEnvironment.UNKNOWN
    business_context: BusinessContext = BusinessContext.GENERAL
    
    # 时间上下文
    analysis_time: datetime = field(default_factory=datetime.now)
    time_zone: str = "UTC+8"
    is_business_hours: bool = True
    is_weekend: bool = False
    
    # 网络规模
    expected_user_count: int = 100
    expected_device_count: int = 200
    network_capacity_mbps: int = 1000
    
    # 安全要求
    security_level: str = "medium"  # low, medium, high, critical
    compliance_requirements: List[str] = field(default_factory=list)
    
    # 历史基线
    baseline_traffic_mbps: float = 100.0
    baseline_connections: int = 500
    baseline_protocols: Dict[str, float] = field(default_factory=dict)
    
    # 业务特征
    critical_services: List[str] = field(default_factory=list)
    allowed_protocols: List[str] = field(default_factory=list)
    blocked_protocols: List[str] = field(default_factory=list)
    
    # 自定义标签
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


class ContextAwareAnalyzer:
    """
    上下文感知分析器
    
    结合网络环境、业务场景、历史数据等上下文信息，
    提供个性化的网络分析和建议。
    """
    
    def __init__(self, llm_config: Dict[str, Any]):
        """
        初始化上下文感知分析器
        
        Args:
            llm_config: LLM客户端配置
        """
        self.logger = logging.getLogger(f"{__name__}.ContextAwareAnalyzer")
        
        # 初始化LLM客户端
        try:
            provider = llm_config.get('provider', 'mock')
            self.llm_client = create_llm_client(provider, llm_config)
            self.logger.info(f"上下文感知分析器初始化成功，LLM提供商: {provider}")
        except Exception as e:
            self.logger.error(f"LLM客户端初始化失败: {e}")
            # 使用Mock客户端作为后备
            self.llm_client = create_llm_client('mock', {})
    
    def analyze_with_context(self, analysis_data: Dict[str, Any], 
                           context: AnalysisContext,
                           analysis_type: AnalysisType = AnalysisType.COMPREHENSIVE) -> Dict[str, Any]:
        """
        基于上下文进行网络分析
        
        Args:
            analysis_data: 网络分析数据
            context: 分析上下文
            analysis_type: 分析类型
            
        Returns:
            Dict[str, Any]: 上下文感知的分析结果
        """
        try:
            self.logger.info(f"开始上下文感知分析: {analysis_type.value}")
            
            # 1. 上下文预处理
            enriched_data = self._enrich_data_with_context(analysis_data, context)
            
            # 2. 生成上下文感知的提示词
            system_prompt = self._create_context_aware_system_prompt(context, analysis_type)
            user_prompt = self._create_context_aware_user_prompt(enriched_data, context, analysis_type)
            
            # 3. 调用LLM进行分析
            messages = [
                LLMMessage(role=MessageRole.SYSTEM, content=system_prompt),
                LLMMessage(role=MessageRole.USER, content=user_prompt)
            ]
            
            llm_response = self.llm_client.chat(messages, temperature=0.3, max_tokens=2000)
            
            # 4. 后处理分析结果
            result = self._post_process_analysis_result(
                llm_response.content, 
                enriched_data, 
                context, 
                analysis_type
            )
            
            self.logger.info("上下文感知分析完成")
            return result
            
        except Exception as e:
            error_msg = f"上下文感知分析失败: {str(e)}"
            self.logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "analysis_type": analysis_type.value,
                "context": context
            }
    
    def _enrich_data_with_context(self, data: Dict[str, Any], 
                                context: AnalysisContext) -> Dict[str, Any]:
        """
        使用上下文信息丰富分析数据
        
        Args:
            data: 原始分析数据
            context: 分析上下文
            
        Returns:
            Dict[str, Any]: 丰富后的分析数据
        """
        enriched_data = data.copy()
        
        # 添加上下文信息
        enriched_data['context'] = {
            'environment': context.network_environment.value,
            'business': context.business_context.value,
            'security_level': context.security_level,
            'analysis_time': context.analysis_time.isoformat(),
            'is_business_hours': context.is_business_hours,
            'is_weekend': context.is_weekend
        }
        
        # 计算相对指标
        if 'traffic_stats' in data:
            traffic_stats = data['traffic_stats']
            current_mbps = traffic_stats.get('bits_per_second', 0) / 1000000
            
            enriched_data['context']['traffic_comparison'] = {
                'current_mbps': current_mbps,
                'baseline_mbps': context.baseline_traffic_mbps,
                'utilization_ratio': current_mbps / context.network_capacity_mbps if context.network_capacity_mbps > 0 else 0,
                'vs_baseline_ratio': current_mbps / context.baseline_traffic_mbps if context.baseline_traffic_mbps > 0 else 0
            }
        
        # 协议合规性检查
        if 'protocol_stats' in data:
            protocol_counts = data['protocol_stats'].get('protocol_counts', {})
            enriched_data['context']['protocol_compliance'] = {
                'allowed_protocols_used': [p for p in protocol_counts.keys() if p in context.allowed_protocols],
                'blocked_protocols_detected': [p for p in protocol_counts.keys() if p in context.blocked_protocols],
                'unknown_protocols': [p for p in protocol_counts.keys() 
                                    if p not in context.allowed_protocols and p not in context.blocked_protocols]
            }
        
        return enriched_data
    
    def _create_context_aware_system_prompt(self, context: AnalysisContext, 
                                          analysis_type: AnalysisType) -> str:
        """
        创建上下文感知的系统提示词
        
        Args:
            context: 分析上下文
            analysis_type: 分析类型
            
        Returns:
            str: 上下文感知的系统提示词
        """
        base_prompt = NetworkAnalysisPrompts.get_system_prompt(analysis_type)
        
        # 添加环境特定的上下文
        context_info = f"""
        
**分析上下文信息**：
- 网络环境：{context.network_environment.value}
- 业务场景：{context.business_context.value}
- 安全等级：{context.security_level}
- 分析时间：{context.analysis_time.strftime('%Y-%m-%d %H:%M:%S')}
- 工作时间：{'是' if context.is_business_hours else '否'}
- 周末：{'是' if context.is_weekend else '否'}
- 预期用户数：{context.expected_user_count}
- 网络容量：{context.network_capacity_mbps} Mbps"""
        
        if context.critical_services:
            context_info += f"\n- 关键服务：{', '.join(context.critical_services)}"
        
        if context.compliance_requirements:
            context_info += f"\n- 合规要求：{', '.join(context.compliance_requirements)}"
        
        # 添加环境特定的分析要求
        env_specific_requirements = self._get_environment_specific_requirements(context)
        
        return base_prompt + context_info + "\n\n" + env_specific_requirements
    
    def _get_environment_specific_requirements(self, context: AnalysisContext) -> str:
        """
        获取环境特定的分析要求
        
        Args:
            context: 分析上下文
            
        Returns:
            str: 环境特定的分析要求
        """
        requirements = {
            NetworkEnvironment.ENTERPRISE: """
**企业网络分析要求**：
- 重点关注生产力和安全性平衡
- 考虑员工访问模式和业务应用需求
- 评估远程办公和VPN使用情况
- 关注数据泄露防护和访问控制""",
            
            NetworkEnvironment.DATA_CENTER: """
**数据中心分析要求**：
- 重点关注高可用性和性能优化
- 评估服务器间通信和负载均衡
- 关注存储网络和备份流量
- 监控虚拟化和容器网络""",
            
            NetworkEnvironment.CLOUD: """
**云环境分析要求**：
- 重点关注成本优化和弹性扩展
- 评估云服务API调用和数据传输
- 关注多租户安全和网络隔离
- 监控云原生应用的网络模式""",
            
            NetworkEnvironment.IOT: """
**物联网分析要求**：
- 重点关注设备安全和异常行为
- 评估设备通信模式和协议使用
- 关注固件更新和设备管理流量
- 监控传感器数据和控制命令""",
            
            NetworkEnvironment.INDUSTRIAL: """
**工业网络分析要求**：
- 重点关注实时性和可靠性
- 评估工控协议和SCADA通信
- 关注生产安全和操作连续性
- 监控关键控制系统的网络行为"""
        }
        
        business_requirements = {
            BusinessContext.FINANCIAL: """
**金融业务要求**：
- 严格的合规性检查（PCI DSS、SOX等）
- 高度关注数据加密和访问控制
- 监控交易系统和风控系统通信
- 防范金融欺诈和洗钱活动""",
            
            BusinessContext.HEALTHCARE: """
**医疗业务要求**：
- HIPAA合规性检查
- 患者数据隐私保护
- 医疗设备网络安全
- 电子病历系统监控""",
            
            BusinessContext.GOVERNMENT: """
**政府业务要求**：
- 国家安全和信息保密
- 严格的访问控制和审计
- 关键基础设施保护
- 公民数据隐私保护"""
        }
        
        env_req = requirements.get(context.network_environment, "")
        business_req = business_requirements.get(context.business_context, "")
        
        return env_req + "\n" + business_req
    
    def _create_context_aware_user_prompt(self, data: Dict[str, Any], 
                                        context: AnalysisContext,
                                        analysis_type: AnalysisType) -> str:
        """
        创建上下文感知的用户提示词
        
        Args:
            data: 丰富后的分析数据
            context: 分析上下文
            analysis_type: 分析类型
            
        Returns:
            str: 上下文感知的用户提示词
        """
        # 使用基础提示词模板
        base_prompt = NetworkAnalysisPrompts.create_analysis_prompt(analysis_type, data)
        
        # 添加上下文特定的分析要求
        context_prompt = "\n\n**上下文特定分析要求**：\n"
        
        # 时间上下文分析
        if not context.is_business_hours:
            context_prompt += "• 当前为非工作时间，请评估是否存在异常的业务活动\n"
        
        if context.is_weekend:
            context_prompt += "• 当前为周末，请关注是否有异常的系统维护或攻击活动\n"
        
        # 容量和基线比较
        if 'traffic_comparison' in data.get('context', {}):
            comparison = data['context']['traffic_comparison']
            context_prompt += f"• 当前流量 {comparison['current_mbps']:.1f} Mbps，"
            context_prompt += f"基线流量 {comparison['baseline_mbps']:.1f} Mbps，"
            context_prompt += f"网络利用率 {comparison['utilization_ratio']*100:.1f}%\n"
        
        # 协议合规性
        if 'protocol_compliance' in data.get('context', {}):
            compliance = data['context']['protocol_compliance']
            if compliance['blocked_protocols_detected']:
                context_prompt += f"• 检测到被禁止的协议：{', '.join(compliance['blocked_protocols_detected'])}\n"
            if compliance['unknown_protocols']:
                context_prompt += f"• 检测到未知协议：{', '.join(compliance['unknown_protocols'])}\n"
        
        # 安全等级要求
        if context.security_level == "critical":
            context_prompt += "• 这是关键安全环境，请提供最严格的安全分析和建议\n"
        elif context.security_level == "high":
            context_prompt += "• 这是高安全要求环境，请重点关注安全威胁和风险\n"
        
        return base_prompt + context_prompt
    
    def _post_process_analysis_result(self, llm_response: str, 
                                    data: Dict[str, Any],
                                    context: AnalysisContext,
                                    analysis_type: AnalysisType) -> Dict[str, Any]:
        """
        后处理分析结果
        
        Args:
            llm_response: LLM响应内容
            data: 分析数据
            context: 分析上下文
            analysis_type: 分析类型
            
        Returns:
            Dict[str, Any]: 处理后的分析结果
        """
        result = {
            "success": True,
            "analysis_type": analysis_type.value,
            "analysis_time": datetime.now().isoformat(),
            "context": {
                "environment": context.network_environment.value,
                "business": context.business_context.value,
                "security_level": context.security_level
            },
            "ai_analysis": llm_response,
            "recommendations": self._extract_recommendations(llm_response),
            "risk_level": self._assess_risk_level(data, context),
            "priority_actions": self._identify_priority_actions(llm_response),
            "metadata": {
                "llm_provider": self.llm_client.provider.value,
                "llm_model": self.llm_client.model,
                "context_tags": context.tags
            }
        }
        
        return result
    
    def _extract_recommendations(self, llm_response: str) -> List[str]:
        """
        从LLM响应中提取建议
        
        Args:
            llm_response: LLM响应内容
            
        Returns:
            List[str]: 提取的建议列表
        """
        recommendations = []
        lines = llm_response.split('\n')
        
        in_recommendations_section = False
        for line in lines:
            line = line.strip()
            if '优化建议' in line or '建议' in line:
                in_recommendations_section = True
                continue
            
            if in_recommendations_section:
                if line.startswith('•') or line.startswith('-') or line.startswith('*'):
                    recommendations.append(line[1:].strip())
                elif line.startswith(('1.', '2.', '3.', '4.', '5.')):
                    recommendations.append(line[2:].strip())
                elif line and not line.startswith('#') and len(recommendations) < 10:
                    recommendations.append(line)
        
        return recommendations[:5]  # 返回前5个建议
    
    def _assess_risk_level(self, data: Dict[str, Any], context: AnalysisContext) -> str:
        """
        评估风险等级
        
        Args:
            data: 分析数据
            context: 分析上下文
            
        Returns:
            str: 风险等级
        """
        risk_score = 0
        
        # 基于异常和威胁数量
        anomalies = data.get('anomalies', [])
        threats = data.get('security_threats', [])
        
        risk_score += len(anomalies) * 10
        risk_score += len(threats) * 20
        
        # 基于协议合规性
        if 'context' in data and 'protocol_compliance' in data['context']:
            compliance = data['context']['protocol_compliance']
            risk_score += len(compliance.get('blocked_protocols_detected', [])) * 30
        
        # 基于安全等级调整
        if context.security_level == "critical":
            risk_score *= 1.5
        elif context.security_level == "high":
            risk_score *= 1.2
        
        # 确定风险等级
        if risk_score >= 100:
            return "critical"
        elif risk_score >= 50:
            return "high"
        elif risk_score >= 20:
            return "medium"
        else:
            return "low"
    
    def _identify_priority_actions(self, llm_response: str) -> List[str]:
        """
        识别优先级行动
        
        Args:
            llm_response: LLM响应内容
            
        Returns:
            List[str]: 优先级行动列表
        """
        priority_actions = []
        lines = llm_response.split('\n')
        
        # 查找包含优先级关键词的行
        priority_keywords = ['紧急', '立即', '优先', '关键', '重要', '首先']
        
        for line in lines:
            line = line.strip()
            if any(keyword in line for keyword in priority_keywords):
                if line.startswith(('•', '-', '*', '1.', '2.', '3.')):
                    priority_actions.append(line)
                elif len(line) > 10:  # 避免添加太短的行
                    priority_actions.append(line)
        
        return priority_actions[:3]  # 返回前3个优先级行动
