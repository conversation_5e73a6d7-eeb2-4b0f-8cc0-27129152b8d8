#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI集成模块
AI Integration Module

集成大语言模型，提供智能网络分析和建议功能。
包含LLM客户端、提示词模板、上下文感知分析等功能。
"""

from .llm_client import (
    LLMClient, LLMMessage, LLMResponse, LLMProvider, MessageRole,
    LLMClientError, MockLLMClient, create_llm_client
)

from .prompt_templates import (
    NetworkAnalysisPrompts, AnalysisType
)

from .context_analyzer import (
    ContextAwareAnalyzer, AnalysisContext,
    NetworkEnvironment, BusinessContext
)

try:
    from .openai_client import OpenAIClient
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

__all__ = [
    # LLM客户端相关
    'LLMClient',
    'LLMMessage',
    'LLMResponse',
    'LLMProvider',
    'MessageRole',
    'LLMClientError',
    'MockLLMClient',
    'create_llm_client',

    # 提示词模板
    'NetworkAnalysisPrompts',
    'AnalysisType',

    # 上下文感知分析
    'ContextAwareAnalyzer',
    'AnalysisContext',
    'NetworkEnvironment',
    'BusinessContext',

    # 可用性标志
    'OPENAI_AVAILABLE'
]

# 如果OpenAI可用，添加到导出列表
if OPENAI_AVAILABLE:
    __all__.append('OpenAIClient')