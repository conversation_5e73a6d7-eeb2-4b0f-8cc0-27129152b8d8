#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络分析器演示脚本（修正版）
Network Analyzer Demo Script (Fixed)

演示网络分析器的各种功能，包括协议统计、流量分析、异常检测等。
"""

import json
from datetime import datetime, timedelta
from src.netanalysis.core.analyzer import NetworkAnalyzer, AnalysisResult
from src.netanalysis.core.models import Packet, ProtocolType, PacketDirection


def create_demo_packets():
    """创建演示用的数据包"""
    base_time = datetime.now()
    packets = []
    
    # 模拟Web浏览流量
    for i in range(20):
        # HTTP请求
        packets.append(Packet(
            timestamp=base_time + timedelta(seconds=i),
            size=200 + i * 10,
            src_ip='*************',
            dst_ip='*************',
            src_port=12345 + i,
            dst_port=80,
            protocol=ProtocolType.TCP,
            direction=PacketDirection.OUTBOUND
        ))
        
        # HTTP响应
        packets.append(Packet(
            timestamp=base_time + timedelta(seconds=i, milliseconds=500),
            size=1500 + i * 50,
            src_ip='*************',
            dst_ip='*************',
            src_port=80,
            dst_port=12345 + i,
            protocol=ProtocolType.TCP,
            direction=PacketDirection.INBOUND
        ))
    
    # 模拟HTTPS流量
    for i in range(10):
        packets.append(Packet(
            timestamp=base_time + timedelta(seconds=i * 2),
            size=300 + i * 20,
            src_ip='*************',
            dst_ip='**************',  # Google
            src_port=13000 + i,
            dst_port=443,
            protocol=ProtocolType.TCP,
            direction=PacketDirection.OUTBOUND
        ))
    
    # 模拟DNS查询
    for i in range(15):
        # DNS查询
        packets.append(Packet(
            timestamp=base_time + timedelta(seconds=i * 3),
            size=64,
            src_ip='*************',
            dst_ip='*******',
            src_port=14000 + i,
            dst_port=53,
            protocol=ProtocolType.UDP,
            direction=PacketDirection.OUTBOUND
        ))
        
        # DNS响应
        packets.append(Packet(
            timestamp=base_time + timedelta(seconds=i * 3, milliseconds=100),
            size=128,
            src_ip='*******',
            dst_ip='*************',
            src_port=53,
            dst_port=14000 + i,
            protocol=ProtocolType.UDP,
            direction=PacketDirection.INBOUND
        ))
    
    # 模拟SSH连接
    for i in range(5):
        packets.append(Packet(
            timestamp=base_time + timedelta(seconds=i * 10),
            size=100 + i * 5,
            src_ip='*************',
            dst_ip='************',
            src_port=15000 + i,
            dst_port=22,
            protocol=ProtocolType.TCP,
            direction=PacketDirection.OUTBOUND
        ))
    
    # 模拟一些异常流量（大量ICMP）
    for i in range(50):
        packets.append(Packet(
            timestamp=base_time + timedelta(seconds=60 + i),
            size=32,
            src_ip='********',
            dst_ip='*************',
            protocol=ProtocolType.ICMP,
            direction=PacketDirection.INBOUND
        ))
    
    return packets


def main():
    """主演示函数"""
    print("🚀 网络数据包分析工具演示")
    print("=" * 50)
    
    # 创建分析器
    config = {
        'top_n_limit': 5,
        'enable_geo_analysis': True,
        'anomaly_threshold': 2.0
    }
    analyzer = NetworkAnalyzer(config)
    
    # 创建演示数据
    print("📦 创建演示数据包...")
    packets = create_demo_packets()
    print(f"   创建了 {len(packets)} 个数据包")
    
    # 创建分析结果对象
    result = AnalysisResult("demo_analysis")
    
    print("\n📊 开始协议统计分析...")
    # 协议统计分析
    result.protocol_stats = analyzer.analyze_protocols(packets)
    print(f"   总数据包: {result.protocol_stats.total_packets}")
    print(f"   总字节数: {result.protocol_stats.total_bytes}")
    print(f"   协议分布: {result.protocol_stats.protocol_counts}")
    print(f"   Top源IP: {result.protocol_stats.top_src_ips[:3]}")
    print(f"   Top目标端口: {result.protocol_stats.top_dst_ports[:3]}")
    
    print("\n🌊 开始流量统计分析...")
    # 流量统计分析
    result.traffic_stats = analyzer.analyze_traffic(packets)
    print(f"   包速率: {result.traffic_stats.packets_per_second:.2f} 包/秒")
    print(f"   带宽: {result.traffic_stats.bits_per_second/1000000:.2f} Mbps")
    print(f"   总连接: {result.traffic_stats.total_connections}")
    print(f"   活跃连接: {result.traffic_stats.active_connections}")
    print(f"   平均包大小: {result.traffic_stats.average_packet_size:.1f} 字节")
    
    print("\n🔍 开始应用层协议分析...")
    # 应用层协议分析
    app_analysis = analyzer.analyze_application_protocols(packets)
    print(f"   识别协议: {list(app_analysis['protocol_distribution'].keys())}")
    print(f"   HTTP分析: {app_analysis['http_analysis']}")
    print(f"   DNS分析: {app_analysis['dns_analysis']}")
    
    print("\n🚨 开始异常检测...")
    # 异常检测
    result.anomalies = analyzer.detect_anomalies(packets)
    print(f"   检测到 {len(result.anomalies)} 个异常")
    for anomaly in result.anomalies:
        print(f"   - {anomaly.title}: {anomaly.description}")
    
    print("\n🛡️ 开始安全威胁检测...")
    # 安全威胁检测
    result.security_threats = analyzer.detect_security_threats(packets)
    print(f"   检测到 {len(result.security_threats)} 个威胁")
    for threat in result.security_threats:
        print(f"   - {threat.title}: {threat.description}")
    
    print("\n💡 获取协议洞察...")
    # 协议洞察
    insights = analyzer.get_protocol_insights(packets)
    print(f"   网络使用模式:")
    for key, value in insights['network_usage_pattern'].items():
        print(f"     {key}: {value}")
    
    print(f"   安全观察:")
    for obs in insights['security_observations']:
        print(f"     - {obs}")
    
    print(f"   优化建议:")
    for rec in insights['recommendations']:
        print(f"     - {rec}")
    
    print("\n📋 生成分析报告...")
    # 生成完整报告
    summary = result.get_summary()
    print(f"   分析ID: {summary['analysis_id']}")
    print(f"   分析时间: {summary['timestamp']}")
    print(f"   数据包总数: {summary['total_packets']}")
    print(f"   数据总量: {summary['total_bytes']} 字节")
    print(f"   分析时长: {summary['duration_seconds']:.2f} 秒")
    print(f"   协议种类: {summary['unique_protocols']}")
    print(f"   异常数量: {summary['anomaly_count']}")
    print(f"   威胁数量: {summary['threat_count']}")
    print(f"   高危问题: {summary['high_severity_issues']}")
    
    print("\n💾 保存分析结果...")
    # 保存结果到文件
    with open('demo_analysis_result.json', 'w', encoding='utf-8') as f:
        json.dump(result.to_dict(), f, ensure_ascii=False, indent=2, default=str)
    print("   结果已保存到 demo_analysis_result.json")
    
    print("\n✅ 演示完成！")
    print("=" * 50)
    print("🎉 网络分析器功能演示成功完成")
    print("📈 所有核心功能均正常工作：")
    print("   ✓ 协议统计分析")
    print("   ✓ 流量统计分析") 
    print("   ✓ 应用层协议识别")
    print("   ✓ 异常检测")
    print("   ✓ 安全威胁检测")
    print("   ✓ 协议洞察分析")


if __name__ == '__main__':
    main()
