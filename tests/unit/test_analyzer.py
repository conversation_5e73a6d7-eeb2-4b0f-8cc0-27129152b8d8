#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络分析器测试模块
Network Analyzer Test Module

测试网络分析器的各种功能，包括协议统计、流量分析、异常检测等。
"""

import unittest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from src.netanalysis.core.analyzer import NetworkAnalyzer, AnalysisResult
from src.netanalysis.core.models import (
    Packet, ProtocolType, PacketDirection, ThreatLevel, AnomalyType
)
from src.netanalysis.core.exceptions import AnalysisError


class TestNetworkAnalyzer(unittest.TestCase):
    """网络分析器测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.analyzer = NetworkAnalyzer()
        self.base_time = datetime.now()
        
        # 创建测试数据包
        self.test_packets = [
            Packet(
                timestamp=self.base_time,
                size=64,
                src_ip="*************",
                dst_ip="***********",
                src_port=12345,
                dst_port=80,
                protocol=ProtocolType.TCP,
                direction=PacketDirection.OUTBOUND
            ),
            Packet(
                timestamp=self.base_time + timedelta(seconds=1),
                size=1500,
                src_ip="***********",
                dst_ip="*************",
                src_port=80,
                dst_port=12345,
                protocol=ProtocolType.TCP,
                direction=PacketDirection.INBOUND
            ),
            Packet(
                timestamp=self.base_time + timedelta(seconds=2),
                size=32,
                src_ip="*************",
                dst_ip="*******",
                src_port=53,
                dst_port=53,
                protocol=ProtocolType.UDP,
                direction=PacketDirection.OUTBOUND
            )
        ]
    
    def test_analyzer_initialization(self):
        """测试分析器初始化"""
        # 测试默认配置
        analyzer = NetworkAnalyzer()
        self.assertEqual(analyzer.top_n_limit, 10)
        self.assertFalse(analyzer.enable_geo_analysis)
        self.assertEqual(analyzer.anomaly_threshold, 3.0)
        
        # 测试自定义配置
        config = {
            'top_n_limit': 20,
            'enable_geo_analysis': True,
            'anomaly_threshold': 2.5
        }
        analyzer = NetworkAnalyzer(config)
        self.assertEqual(analyzer.top_n_limit, 20)
        self.assertTrue(analyzer.enable_geo_analysis)
        self.assertEqual(analyzer.anomaly_threshold, 2.5)
    
    def test_analyze_protocols_empty_packets(self):
        """测试空数据包列表的协议分析"""
        result = self.analyzer.analyze_protocols([])
        self.assertEqual(result.total_packets, 0)
        self.assertEqual(result.total_bytes, 0)
        self.assertEqual(len(result.protocol_counts), 0)
    
    def test_analyze_protocols_basic(self):
        """测试基础协议分析功能"""
        result = self.analyzer.analyze_protocols(self.test_packets)
        
        # 验证基础统计
        self.assertEqual(result.total_packets, 3)
        self.assertEqual(result.total_bytes, 64 + 1500 + 32)
        self.assertEqual(result.unique_flows, 2)  # TCP连接和UDP查询
        
        # 验证协议分布
        self.assertEqual(result.protocol_counts['tcp'], 2)
        self.assertEqual(result.protocol_counts['udp'], 1)
        
        # 验证协议百分比
        self.assertAlmostEqual(result.protocol_percentages['tcp'], 66.67, places=1)
        self.assertAlmostEqual(result.protocol_percentages['udp'], 33.33, places=1)
        
        # 验证端口统计
        src_ports = dict(result.top_src_ports)
        dst_ports = dict(result.top_dst_ports)
        self.assertEqual(src_ports[12345], 1)
        self.assertEqual(src_ports[80], 1)
        self.assertEqual(dst_ports[80], 1)
        self.assertEqual(dst_ports[53], 1)
        
        # 验证IP统计
        src_ips = dict(result.top_src_ips)
        dst_ips = dict(result.top_dst_ips)
        self.assertEqual(src_ips["*************"], 2)
        self.assertEqual(dst_ips["***********"], 1)
        self.assertEqual(dst_ips["*******"], 1)
    
    def test_analyze_traffic_empty_packets(self):
        """测试空数据包列表的流量分析"""
        result = self.analyzer.analyze_traffic([])
        self.assertEqual(result.total_packets, 0)
        self.assertEqual(result.total_bytes, 0)
    
    def test_analyze_traffic_basic(self):
        """测试基础流量分析功能"""
        result = self.analyzer.analyze_traffic(self.test_packets)
        
        # 验证基础统计
        self.assertEqual(result.total_packets, 3)
        self.assertEqual(result.total_bytes, 64 + 1500 + 32)
        
        # 验证速率计算
        self.assertGreater(result.packets_per_second, 0)
        self.assertGreater(result.bytes_per_second, 0)
        self.assertGreater(result.bits_per_second, 0)
        
        # 验证平均包大小
        expected_avg_size = (64 + 1500 + 32) / 3
        self.assertAlmostEqual(result.average_packet_size, expected_avg_size, places=1)
        
        # 验证连接统计
        self.assertGreaterEqual(result.total_connections, 1)

        # 注意：由于TrafficStats模型的字段限制，我们只验证基础统计
        # 详细的TCP/UDP统计在协议分析中进行
    
    def test_detect_anomalies_empty_packets(self):
        """测试空数据包列表的异常检测"""
        result = self.analyzer.detect_anomalies([])
        self.assertEqual(len(result), 0)
    
    def test_detect_security_threats_empty_packets(self):
        """测试空数据包列表的安全威胁检测"""
        result = self.analyzer.detect_security_threats([])
        self.assertEqual(len(result), 0)
    
    def test_ddos_detection(self):
        """测试DDoS攻击检测"""
        # 创建模拟DDoS攻击的数据包（大量包指向同一目标）
        ddos_packets = []
        target_ip = "*************"
        
        for i in range(100):
            packet = Packet(
                timestamp=self.base_time + timedelta(seconds=i),
                size=64,
                src_ip=f"10.0.0.{i % 10}",
                dst_ip=target_ip,
                src_port=12345 + i,
                dst_port=80,
                protocol=ProtocolType.TCP,
                direction=PacketDirection.INBOUND
            )
            ddos_packets.append(packet)
        
        threats = self.analyzer.detect_security_threats(ddos_packets)
        
        # 应该检测到DDoS威胁
        ddos_threats = [t for t in threats if t.threat_type == "ddos"]
        self.assertGreater(len(ddos_threats), 0)
        
        # 验证威胁详情
        threat = ddos_threats[0]
        self.assertEqual(threat.severity, ThreatLevel.HIGH)
        self.assertIn(target_ip, threat.target_ips)
    
    def test_port_scan_detection(self):
        """测试端口扫描检测"""
        # 创建模拟端口扫描的数据包
        scan_packets = []
        scanner_ip = "********"
        target_ip = "*************"
        
        for port in range(1, 100):  # 扫描99个端口
            packet = Packet(
                timestamp=self.base_time + timedelta(seconds=port),
                size=64,
                src_ip=scanner_ip,
                dst_ip=target_ip,
                src_port=12345,
                dst_port=port,
                protocol=ProtocolType.TCP,
                direction=PacketDirection.OUTBOUND
            )
            scan_packets.append(packet)
        
        threats = self.analyzer.detect_security_threats(scan_packets)
        
        # 应该检测到端口扫描威胁
        scan_threats = [t for t in threats if t.threat_type == "port_scan"]
        self.assertGreater(len(scan_threats), 0)
        
        # 验证威胁详情
        threat = scan_threats[0]
        self.assertEqual(threat.severity, ThreatLevel.MEDIUM)
        self.assertIn(scanner_ip, threat.source_ips)


class TestAnalysisResult(unittest.TestCase):
    """分析结果测试类"""
    
    def test_analysis_result_initialization(self):
        """测试分析结果初始化"""
        result = AnalysisResult()
        self.assertIsNotNone(result.analysis_id)
        self.assertIsNotNone(result.timestamp)
        self.assertIsNone(result.protocol_stats)
        self.assertIsNone(result.traffic_stats)
        self.assertEqual(len(result.anomalies), 0)
        self.assertEqual(len(result.security_threats), 0)
    
    def test_analysis_result_to_dict(self):
        """测试分析结果转换为字典"""
        result = AnalysisResult("test_analysis")
        result_dict = result.to_dict()
        
        self.assertEqual(result_dict["analysis_id"], "test_analysis")
        self.assertIn("timestamp", result_dict)
        self.assertIsNone(result_dict["protocol_stats"])
        self.assertIsNone(result_dict["traffic_stats"])
        self.assertEqual(len(result_dict["anomalies"]), 0)
        self.assertEqual(len(result_dict["security_threats"]), 0)
    
    def test_analysis_result_summary(self):
        """测试分析结果摘要"""
        result = AnalysisResult("test_analysis")
        summary = result.get_summary()
        
        self.assertEqual(summary["analysis_id"], "test_analysis")
        self.assertEqual(summary["total_packets"], 0)
        self.assertEqual(summary["total_bytes"], 0)
        self.assertEqual(summary["anomaly_count"], 0)
        self.assertEqual(summary["threat_count"], 0)


if __name__ == '__main__':
    unittest.main()
