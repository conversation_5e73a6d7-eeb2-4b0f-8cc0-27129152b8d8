#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PCAP解析器单元测试
PCAP Parser Unit Tests

测试PCAP解析器的各种功能，包括：
- 文件格式检测
- 数据包解析
- 元数据提取
- 错误处理
"""

import pytest
import tempfile
import struct
from datetime import datetime
from pathlib import Path

from netanalysis.parsers.pcap_parser import PcapParser
from netanalysis.core.exceptions import ParseError, FileFormatError
from netanalysis.core.models import ProtocolType


class TestPcapParser:
    """PCAP解析器测试类"""
    
    def setup_method(self):
        """测试方法初始化"""
        self.parser = PcapParser()
    
    def test_supported_extensions(self):
        """测试支持的文件扩展名"""
        expected_extensions = ['pcap', 'cap', 'dmp']
        assert self.parser.supported_extensions == expected_extensions
    
    def test_can_parse_valid_pcap_file(self):
        """测试能否识别有效的PCAP文件"""
        # 创建一个简单的PCAP文件头
        with tempfile.NamedTemporaryFile(suffix='.pcap', delete=False) as f:
            # PCAP全局头部
            global_header = struct.pack('<IHHIIII',
                0xa1b2c3d4,  # 魔数
                2, 4,        # 版本号
                0,           # 时区偏移
                0,           # 时间戳精度
                65535,       # 快照长度
                1            # 数据链路类型（以太网）
            )
            f.write(global_header)
            f.flush()
            
            # 测试文件识别
            assert self.parser.can_parse(f.name)
            
            # 清理
            Path(f.name).unlink()
    
    def test_can_parse_invalid_file(self):
        """测试无法识别无效文件"""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            f.write('这不是PCAP文件'.encode('utf-8'))
            f.flush()

            # 测试文件识别
            assert not self.parser.can_parse(f.name)

            # 清理
            Path(f.name).unlink()
    
    def test_can_parse_wrong_extension(self):
        """测试错误扩展名的文件"""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            # 即使有正确的PCAP头，扩展名错误也应该返回False
            global_header = struct.pack('<IHHIIII',
                0xa1b2c3d4, 2, 4, 0, 0, 65535, 1
            )
            f.write(global_header)
            f.flush()
            
            assert not self.parser.can_parse(f.name)
            
            # 清理
            Path(f.name).unlink()
    
    def test_parse_empty_pcap_file(self):
        """测试解析空的PCAP文件"""
        with tempfile.NamedTemporaryFile(suffix='.pcap', delete=False) as f:
            # 只写入全局头部，没有数据包
            global_header = struct.pack('<IHHIIII',
                0xa1b2c3d4, 2, 4, 0, 0, 65535, 1
            )
            f.write(global_header)
            f.flush()
            
            # 解析文件
            packets = list(self.parser.parse_file(f.name))
            assert len(packets) == 0
            
            # 检查统计信息
            stats = self.parser.get_parse_stats()
            assert stats['total_packets'] == 0
            assert stats['parsed_packets'] == 0
            
            # 清理
            Path(f.name).unlink()
    
    def test_get_file_metadata(self):
        """测试获取文件元数据"""
        with tempfile.NamedTemporaryFile(suffix='.pcap', delete=False) as f:
            # 创建带有全局头部的PCAP文件
            global_header = struct.pack('<IHHIIII',
                0xa1b2c3d4, 2, 4, 0, 0, 65535, 1
            )
            f.write(global_header)
            f.flush()
            
            # 获取元数据
            metadata = self.parser.get_file_metadata(f.name)
            
            # 验证基础信息
            assert metadata.filename == Path(f.name).name
            assert metadata.file_format == 'pcap'
            assert metadata.file_size > 0
            assert metadata.total_packets == 0  # 空文件
            
            # 验证注释信息
            assert any('PCAP版本' in comment for comment in metadata.comments)
            
            # 清理
            Path(f.name).unlink()
    
    def test_parse_invalid_file_format(self):
        """测试解析无效格式文件时的错误处理"""
        with tempfile.NamedTemporaryFile(suffix='.pcap', delete=False) as f:
            f.write('无效的PCAP文件内容'.encode('utf-8'))
            f.flush()

            # 应该抛出FileFormatError
            with pytest.raises(FileFormatError):
                list(self.parser.parse_file(f.name))

            # 清理
            Path(f.name).unlink()
    
    def test_parser_config(self):
        """测试解析器配置"""
        config = {
            'chunk_size': 2048,
            'max_packets': 100,
            'extract_payload': True,
            'max_payload_size': 512
        }
        
        parser = PcapParser(config)
        
        assert parser.chunk_size == 2048
        assert parser.max_packets == 100
        assert parser.extract_payload == True
        assert parser.max_payload_size == 512
    
    def test_stats_tracking(self):
        """测试统计信息跟踪"""
        # 重置统计信息
        self.parser.reset_stats()
        
        stats = self.parser.get_parse_stats()
        assert stats['total_packets'] == 0
        assert stats['parsed_packets'] == 0
        assert stats['failed_packets'] == 0
        assert stats['start_time'] is None
        assert stats['end_time'] is None
    
    def test_packet_validation(self):
        """测试数据包验证"""
        from netanalysis.core.models import Packet
        
        # 创建有效数据包
        valid_packet = Packet(
            timestamp=datetime.now(),
            size=64,
            src_ip='***********',
            dst_ip='***********',
            src_port=80,
            dst_port=8080,
            protocol=ProtocolType.TCP
        )
        
        assert self.parser._validate_packet(valid_packet)
        
        # 创建无效数据包（负数大小）
        invalid_packet = Packet(
            timestamp=datetime.now(),
            size=-1
        )
        
        assert not self.parser._validate_packet(invalid_packet)
    
    def test_determine_packet_direction(self):
        """测试数据包方向判断"""
        from netanalysis.core.models import Packet, PacketDirection
        
        # 内网到外网（出站）
        outbound_packet = Packet(
            timestamp=datetime.now(),
            size=64,
            src_ip='***********',
            dst_ip='*******'
        )
        
        direction = self.parser._determine_packet_direction(outbound_packet)
        assert direction == PacketDirection.OUTBOUND
        
        # 外网到内网（入站）
        inbound_packet = Packet(
            timestamp=datetime.now(),
            size=64,
            src_ip='*******',
            dst_ip='***********'
        )
        
        direction = self.parser._determine_packet_direction(inbound_packet)
        assert direction == PacketDirection.INBOUND
        
        # 内网到内网（内部）
        internal_packet = Packet(
            timestamp=datetime.now(),
            size=64,
            src_ip='***********',
            dst_ip='***********'
        )
        
        direction = self.parser._determine_packet_direction(internal_packet)
        assert direction == PacketDirection.INTERNAL
    
    def test_string_representation(self):
        """测试字符串表示"""
        parser_str = str(self.parser)
        assert 'PcapParser' in parser_str
        assert 'pcap' in parser_str
        
        parser_repr = repr(self.parser)
        assert 'PcapParser' in parser_repr
        assert 'extensions' in parser_repr


@pytest.fixture
def sample_pcap_data():
    """创建示例PCAP数据的fixture"""
    # 这里可以创建更复杂的测试数据
    return {
        'global_header': struct.pack('<IHHIIII',
            0xa1b2c3d4, 2, 4, 0, 0, 65535, 1
        ),
        'packet_count': 0
    }


class TestPcapParserIntegration:
    """PCAP解析器集成测试"""
    
    def test_full_parsing_workflow(self, sample_pcap_data):
        """测试完整的解析工作流程"""
        parser = PcapParser()
        
        with tempfile.NamedTemporaryFile(suffix='.pcap', delete=False) as f:
            f.write(sample_pcap_data['global_header'])
            f.flush()
            
            # 1. 验证文件
            assert parser.validate_file(f.name)
            
            # 2. 检查是否可以解析
            assert parser.can_parse(f.name)
            
            # 3. 获取元数据
            metadata = parser.get_file_metadata(f.name)
            assert metadata is not None
            
            # 4. 解析数据包
            packets = parser.parse_packets(f.name)
            assert isinstance(packets, list)
            
            # 5. 获取统计信息
            stats = parser.get_parse_stats()
            assert 'total_packets' in stats
            
            # 清理
            Path(f.name).unlink()
