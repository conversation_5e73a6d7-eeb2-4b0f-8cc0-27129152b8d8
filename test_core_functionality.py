#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心功能测试脚本
Core Functionality Test Script

测试网络数据包分析工具的核心功能，不依赖可选的外部库。
"""

import sys
import logging
from datetime import datetime
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_core_analysis():
    """测试核心分析功能"""
    print("🔍 测试核心分析功能")
    print("-" * 40)
    
    try:
        from src.netanalysis.core.analyzer import NetworkAnalyzer, AnalysisResult
        from src.netanalysis.core.models import Packet, ProtocolType, PacketDirection
        
        # 创建分析器
        analyzer = NetworkAnalyzer()
        print("✅ 网络分析器创建成功")
        
        # 创建测试数据包
        base_time = datetime.now()
        packets = []
        
        for i in range(50):
            packet = Packet(
                timestamp=base_time,
                size=64 + i * 10,
                src_ip=f"192.168.1.{100 + i % 10}",
                dst_ip=f"10.0.0.{1 + i % 5}",
                src_port=12345 + i,
                dst_port=80 if i % 3 == 0 else 443,
                protocol=ProtocolType.TCP if i % 2 == 0 else ProtocolType.UDP,
                direction=PacketDirection.OUTBOUND if i % 2 == 0 else PacketDirection.INBOUND
            )
            packets.append(packet)
        
        print(f"✅ 创建了 {len(packets)} 个测试数据包")
        
        # 执行分析
        result = AnalysisResult("test_analysis")
        result.protocol_stats = analyzer.analyze_protocols(packets)
        result.traffic_stats = analyzer.analyze_traffic(packets)
        result.anomalies = analyzer.detect_anomalies(packets)
        result.security_threats = analyzer.detect_security_threats(packets)
        
        print("✅ 分析完成:")
        print(f"   - 协议统计: {len(result.protocol_stats.protocol_counts)} 种协议")
        print(f"   - 总数据包: {result.protocol_stats.total_packets}")
        print(f"   - 总字节数: {result.protocol_stats.total_bytes}")
        print(f"   - 异常数量: {len(result.anomalies)}")
        print(f"   - 威胁数量: {len(result.security_threats)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 核心分析功能测试失败: {e}")
        return False


def test_ai_integration():
    """测试AI集成功能"""
    print("\n🤖 测试AI集成功能")
    print("-" * 40)
    
    try:
        from src.netanalysis.ai import (
            ContextAwareAnalyzer, AnalysisContext,
            NetworkEnvironment, BusinessContext, AnalysisType
        )
        
        # 创建AI分析器
        ai_analyzer = ContextAwareAnalyzer({'provider': 'mock'})
        print("✅ AI分析器创建成功")
        
        # 创建分析上下文
        context = AnalysisContext(
            network_environment=NetworkEnvironment.ENTERPRISE,
            business_context=BusinessContext.TECHNOLOGY
        )
        print("✅ 分析上下文创建成功")
        
        # 模拟分析数据
        analysis_data = {
            'protocol_stats': {
                'total_packets': 100,
                'total_bytes': 50000,
                'protocol_counts': {'tcp': 60, 'udp': 40}
            }
        }
        
        # 执行AI分析
        ai_result = ai_analyzer.analyze_with_context(
            analysis_data, context, AnalysisType.COMPREHENSIVE
        )
        
        if ai_result['success']:
            print("✅ AI分析完成:")
            print(f"   - 分析类型: {ai_result['analysis_type']}")
            print(f"   - 风险等级: {ai_result['risk_level']}")
            print(f"   - 建议数量: {len(ai_result['recommendations'])}")
        else:
            print(f"⚠️ AI分析失败: {ai_result.get('error', '未知错误')}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI集成功能测试失败: {e}")
        return False


def test_web_api():
    """测试Web API功能"""
    print("\n🌐 测试Web API功能")
    print("-" * 40)
    
    try:
        from src.netanalysis.web import FASTAPI_AVAILABLE
        
        if not FASTAPI_AVAILABLE:
            print("⚠️ FastAPI库未安装，跳过Web API测试")
            return True
        
        from src.netanalysis.web.api import create_app
        
        # 创建Web应用
        app = create_app({
            'title': '测试应用',
            'debug': True
        })
        
        print("✅ Web应用创建成功")
        print("   - FastAPI应用已配置")
        print("   - 路由已注册")
        
        return True
        
    except Exception as e:
        print(f"❌ Web API功能测试失败: {e}")
        return False


def test_cli_commands():
    """测试CLI命令功能"""
    print("\n💻 测试CLI命令功能")
    print("-" * 40)
    
    try:
        from src.netanalysis.cli.commands import (
            analyze_command, config_command, export_command
        )
        
        # 测试配置命令
        config_result = config_command(show=True)
        if config_result['success']:
            print("✅ 配置管理功能正常")
            print(f"   - 配置项数量: {len(config_result['config'])}")
        
        # 创建测试文件
        test_file = Path("test_data.pcap")
        test_file.write_text("test data")
        
        # 测试分析命令
        analyze_result = analyze_command(
            input_file=str(test_file),
            save_results=False,
            verbose=False
        )
        
        if analyze_result['success']:
            print("✅ 文件分析功能正常")
            print(f"   - 分析ID: {analyze_result.get('analysis_id', 'N/A')}")
        
        # 清理测试文件
        test_file.unlink()
        
        return True
        
    except Exception as e:
        print(f"❌ CLI命令功能测试失败: {e}")
        return False


def test_data_models():
    """测试数据模型"""
    print("\n📊 测试数据模型")
    print("-" * 40)
    
    try:
        from src.netanalysis.core.models import (
            Packet, ProtocolType, PacketDirection,
            ProtocolStats, TrafficStats, Anomaly, SecurityThreat
        )
        
        # 测试数据包模型
        packet = Packet(
            timestamp=datetime.now(),
            size=1500,
            src_ip="*************",
            dst_ip="********",
            src_port=12345,
            dst_port=80,
            protocol=ProtocolType.TCP,
            direction=PacketDirection.OUTBOUND
        )
        
        print("✅ 数据包模型创建成功")
        print(f"   - 源IP: {packet.src_ip}")
        print(f"   - 目标IP: {packet.dst_ip}")
        print(f"   - 协议: {packet.protocol.value}")
        
        # 测试协议统计模型
        protocol_stats = ProtocolStats()
        protocol_stats.total_packets = 100
        protocol_stats.total_bytes = 50000
        protocol_stats.protocol_counts = {'tcp': 60, 'udp': 40}
        
        print("✅ 协议统计模型创建成功")
        print(f"   - 总包数: {protocol_stats.total_packets}")
        print(f"   - 协议种类: {len(protocol_stats.protocol_counts)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据模型测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 网络数据包分析工具 - 核心功能测试")
    print("=" * 60)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("数据模型", test_data_models()))
    test_results.append(("核心分析", test_core_analysis()))
    test_results.append(("AI集成", test_ai_integration()))
    test_results.append(("Web API", test_web_api()))
    test_results.append(("CLI命令", test_cli_commands()))
    
    # 显示测试结果
    print("\n📋 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} - {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有核心功能测试通过！")
        return 0
    else:
        print("⚠️ 部分功能测试失败，请检查相关模块")
        return 1


if __name__ == '__main__':
    sys.exit(main())
